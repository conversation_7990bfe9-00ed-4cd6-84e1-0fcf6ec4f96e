################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Source/Lib/SDCard/FAT.c \
../Source/Lib/SDCard/File.c \
../Source/Lib/SDCard/SD_SPI_Erase.c \
../Source/Lib/SDCard/SD_SPI_Initialization.c \
../Source/Lib/SDCard/SD_SPI_Read.c \
../Source/Lib/SDCard/SD_SPI_Registers.c \
../Source/Lib/SDCard/SD_SPI_Transmission.c \
../Source/Lib/SDCard/SD_SPI_Write.c \
../Source/Lib/SDCard/disk_sd.c \
../Source/Lib/SDCard/diskio.c 

C_DEPS += \
./Source/Lib/SDCard/FAT.d \
./Source/Lib/SDCard/File.d \
./Source/Lib/SDCard/SD_SPI_Erase.d \
./Source/Lib/SDCard/SD_SPI_Initialization.d \
./Source/Lib/SDCard/SD_SPI_Read.d \
./Source/Lib/SDCard/SD_SPI_Registers.d \
./Source/Lib/SDCard/SD_SPI_Transmission.d \
./Source/Lib/SDCard/SD_SPI_Write.d \
./Source/Lib/SDCard/disk_sd.d \
./Source/Lib/SDCard/diskio.d 

OBJS += \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/FAT.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/File.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/SD_SPI_Erase.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/SD_SPI_Initialization.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/SD_SPI_Read.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/SD_SPI_Registers.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/SD_SPI_Transmission.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/SD_SPI_Write.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/disk_sd.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/diskio.obj 

OBJS__QUOTED += \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\FAT.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\File.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\SD_SPI_Erase.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\SD_SPI_Initialization.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\SD_SPI_Read.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\SD_SPI_Registers.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\SD_SPI_Transmission.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\SD_SPI_Write.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\disk_sd.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\diskio.obj" 

C_DEPS__QUOTED += \
"Source\Lib\SDCard\FAT.d" \
"Source\Lib\SDCard\File.d" \
"Source\Lib\SDCard\SD_SPI_Erase.d" \
"Source\Lib\SDCard\SD_SPI_Initialization.d" \
"Source\Lib\SDCard\SD_SPI_Read.d" \
"Source\Lib\SDCard\SD_SPI_Registers.d" \
"Source\Lib\SDCard\SD_SPI_Transmission.d" \
"Source\Lib\SDCard\SD_SPI_Write.d" \
"Source\Lib\SDCard\disk_sd.d" \
"Source\Lib\SDCard\diskio.d" 

C_SRCS__QUOTED += \
"../Source/Lib/SDCard/FAT.c" \
"../Source/Lib/SDCard/File.c" \
"../Source/Lib/SDCard/SD_SPI_Erase.c" \
"../Source/Lib/SDCard/SD_SPI_Initialization.c" \
"../Source/Lib/SDCard/SD_SPI_Read.c" \
"../Source/Lib/SDCard/SD_SPI_Registers.c" \
"../Source/Lib/SDCard/SD_SPI_Transmission.c" \
"../Source/Lib/SDCard/SD_SPI_Write.c" \
"../Source/Lib/SDCard/disk_sd.c" \
"../Source/Lib/SDCard/diskio.c" 


