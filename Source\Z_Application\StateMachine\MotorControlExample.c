/**
 * @file MotorControlExample.c
 * @brief Example usage of the motor control interface abstraction layer
 * @note This file demonstrates how to switch between real and virtual motor control
 */

#include "externInputInterface.h"
#include "Lib\BackgroundInterface\BackgroundInterface.h"

/**
 * @brief Example function showing how to use the motor control interface
 * @note This function demonstrates runtime switching between real and virtual control
 */
void MotorControlExample_Demo(void) {
    /* Initialize the motor control interface */
    MotorControlInterface_Init();
    
    /* Example 1: Use real hardware control */
    MotorControlInterface_SwitchToReal();
    
    /* Run motor with real hardware */
    MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 
                             10,      /* CycleTime: 10ms */
                             90.0f,   /* TargetPosition: 90 degrees */
                             30.0f,   /* TargetSpeed: 30 deg/s */
                             50.0f,   /* MaxSpeed: 50 deg/s */
                             1.0f,    /* KP1: Position gain 1 */
                             0.5f);   /* KP2: Position gain 2 */
    
    /* Example 2: Switch to virtual simulation */
    MotorControlInterface_SwitchToVirtual();
    
    /* Run motor with virtual simulation */
    MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 
                             10,      /* CycleTime: 10ms */
                             45.0f,   /* TargetPosition: 45 degrees */
                             20.0f,   /* TargetSpeed: 20 deg/s */
                             40.0f,   /* MaxSpeed: 40 deg/s */
                             1.2f,    /* KP1: Position gain 1 */
                             0.8f);   /* KP2: Position gain 2 */
}

/**
 * @brief Example function for testing different motor control modes
 */
void MotorControlExample_TestModes(void) {
    ENUM_MOTOR_CONTROL_MODE currentMode;
    
    /* Get current mode */
    currentMode = MotorControlInterface_GetMode();
    
    /* Test position control in current mode */
    MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 
                             10,      /* CycleTime: 10ms */
                             0.0f,    /* TargetPosition: 0 degrees (home) */
                             15.0f,   /* TargetSpeed: 15 deg/s */
                             30.0f,   /* MaxSpeed: 30 deg/s */
                             1.0f,    /* KP1 */
                             0.5f);   /* KP2 */
    
    /* Test speed control in current mode */
    MotorControlInterface_Run(MOTION_CONTROL_MODE_SPEED, 
                             10,      /* CycleTime: 10ms */
                             0.0f,    /* TargetPosition: not used in speed mode */
                             10.0f,   /* TargetSpeed: 10 deg/s */
                             25.0f,   /* MaxSpeed: 25 deg/s */
                             0.0f,    /* KP1: not used in speed mode */
                             0.0f);   /* KP2: not used in speed mode */
    
    /* Test stop mode */
    MotorControlInterface_Run(MOTION_CONTROL_MODE_STOP, 
                             10,      /* CycleTime: 10ms */
                             0.0f,    /* TargetPosition: not used */
                             0.0f,    /* TargetSpeed: not used */
                             0.0f,    /* MaxSpeed: not used */
                             0.0f,    /* KP1: not used */
                             0.0f);   /* KP2: not used */
}

/**
 * @brief Example function for runtime mode switching based on conditions
 */
void MotorControlExample_ConditionalSwitching(void) {
    /* Example: Switch to virtual mode for testing, real mode for production */
    
    /* Check if we're in debug/test environment */
    #ifdef DEBUG_USE
        /* Use virtual simulation for testing */
        MotorControlInterface_SwitchToVirtual();
    #else
        /* Use real hardware for production */
        MotorControlInterface_SwitchToReal();
    #endif
    
    /* Run motor control with the selected mode */
    MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 
                             10,      /* CycleTime: 10ms */
                             30.0f,   /* TargetPosition: 30 degrees */
                             25.0f,   /* TargetSpeed: 25 deg/s */
                             50.0f,   /* MaxSpeed: 50 deg/s */
                             1.0f,    /* KP1 */
                             0.5f);   /* KP2 */
}

/**
 * @brief Example function showing how to integrate with existing state machine
 */
void MotorControlExample_StateMachineIntegration(void) {
    /* This function shows how the new interface integrates with existing code */
    
    /* The MotorRun function now automatically uses the interface */
    /* No changes needed in existing state machine code */
    
    /* Example: Normal state motor control (same as before) */
    MotorRun(MOTION_CONTROL_MODE_STOP, 
             0,       /* CycleTime */
             70.0f,   /* TargetPosition: 70 degrees */
             5.0f,    /* TargetSpeed: 5 deg/s */
             5.0f,    /* MaxSpeed: 5 deg/s */
             0.0f,    /* KP1 */
             0.0f);   /* KP2 */
    
    /* The difference is that now you can control whether it uses real or virtual control */
    /* by calling MotorControlInterface_SetMode() before MotorRun() */
}
