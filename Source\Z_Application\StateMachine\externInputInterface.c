/**
 * @file externInputInterface.c
 * @brief External input interface implementation with motor control abstraction
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#include "externInputInterface.h"
#include "Lib\BackgroundInterface\BackgroundInterface.h"
#include "ServoSimCore.h"

/* Static variable to store current motor control mode */
static ENUM_MOTOR_CONTROL_MODE gMotorControlMode = MOTOR_CONTROL_MODE_REAL;

/* External servo simulation state - declared in ServoSimCore */
extern ServoSimState gSTservoSimState;

void Input(void) {
    /* Input from real device */
    //gstSEC.gbDriverIDCheckPassed = TRUE;
    gstSEC.gbSafetyLineFb = TRUE;
    //gstSEC.gbDriverIDCheckPassed = (bool)SystemVariablDI.DI[1].DelaylValue;
    //gstSEC.gbSafetyLineFb = (bool)SystemVariablDI.DI[1].DelaylValue;
}

void Output(void) {
    /* output from real device (unsigned char)*/
    AWS.Write.RelayClose = gstSEC.gbSafetyRelayClose;
}

void MotorControlInterface_Init(void) {
    /* Initialize motor control interface */
    gMotorControlMode = MOTOR_CONTROL_MODE_REAL; /* Default to real hardware mode */
}

void MotorControlInterface_SetMode(ENUM_MOTOR_CONTROL_MODE mode) {
    /* Set motor control mode */
    gMotorControlMode = mode;
}

ENUM_MOTOR_CONTROL_MODE MotorControlInterface_GetMode(void) {
    /* Get current motor control mode */
    return gMotorControlMode;
}

void MotorControlInterface_Run(ENUM_MOTION_CONTROL_MODE_SELECT Mode,
                              unsigned int CycleTime,
                              float TargetPosition,
                              float TargetSpeed,
                              float MaxSpeed,
                              float KP1,
                              float KP2) {
    /* Motor control interface - switch between real and virtual control */
    if (gMotorControlMode == MOTOR_CONTROL_MODE_REAL) {
        /* Use real hardware motion control */
        MotionControlRun(Mode, CycleTime, TargetPosition, TargetSpeed, MaxSpeed, KP1, KP2);
    } else {
        /* Use virtual simulation control */
        /* Convert command parameters to simulation format */
        gSTservoSimState.dPositionCommand = ((float)gstSEC.giCommandMotorPosition * 0.01f) * (float)gstSEC.guiGearRatioAll;
        gSTservoSimState.dSpeedLimit = (float)gstSEC.giCommandMotorSpeedLimit * 0.01f * (float)gstSEC.guiGearRatioAll;

        /* Update simulation state */
        SimulationStep(&gSTservoSimState);

        /* Convert simulation results back to system format */
        if (gstSEC.guiGearRatioAll != 0U) {
            gstSEC.giMotorPosition = (i16)((float)(gSTservoSimState.dMotorPosition * 100.0 / (double)gstSEC.guiGearRatioAll));
            gstSEC.giMotorSpeed = (i16)((float)(gSTservoSimState.dMotorSpeed * 100.0 / (double)gstSEC.guiGearRatioAll));
        }
    }
}

/* Convenience functions for mode switching */
void MotorControlInterface_SwitchToReal(void) {
    /* Switch to real hardware mode */
    MotorControlInterface_SetMode(MOTOR_CONTROL_MODE_REAL);
}

void MotorControlInterface_SwitchToVirtual(void) {
    /* Switch to virtual simulation mode */
    MotorControlInterface_SetMode(MOTOR_CONTROL_MODE_VIRTUAL);
}

