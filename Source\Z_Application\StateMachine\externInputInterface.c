/**
 * @file externInputInterface.c
 * @brief External input interface implementation
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#include "externInputInterface.h"
#include "Lib\BackgroundInterface\BackgroundInterface.h"


void Input(void) {
    /* Input from real device */
    //gstSEC.gbDriverIDCheckPassed = TRUE;
    gstSEC.gbSafetyLineFb = TRUE;
    //gstSEC.gbDriverIDCheckPassed = (bool)SystemVariablDI.DI[1].DelaylValue;
    //gstSEC.gbSafetyLineFb = (bool)SystemVariablDI.DI[1].DelaylValue;
}

void Output(void) {
    /* output from real device (unsigned char)*/
    AWS.Write.RelayClose = gstSEC.gbSafetyRelayClose;
    //MotionControlRun(MOTION_CONTROL_MODE_STOP, unsigned int CycleTime,float TargetPosition, float TargetSpeed,float MaxSpeed, float KP1, float KP2);
}

