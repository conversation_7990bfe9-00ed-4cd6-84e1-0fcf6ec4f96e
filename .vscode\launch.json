{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/CCS_Program/User_SHE_WITHOUT_PLCAugment/Source/Z_Application/StateMachine", "program": "d:/CCS_Program/User_SHE_WITHOUT_PLCAugment/Source/Z_Application/StateMachine/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}