# 电机控制接口快速使用指南

## 🚀 快速开始

### 1. 配置编译模式

编辑 `Include/MotorControlConfig.h` 文件：

#### 开发/测试模式
```c
#define ENABLE_RUNTIME_SWITCH  // 启用运行时切换
```

#### 生产模式
```c
// #define ENABLE_RUNTIME_SWITCH  // 注释掉，禁用运行时切换
```

### 2. 开发测试使用

```c
#include "externInputInterface.h"

void test_motor_control(void) {
    // 系统会自动初始化（在StateMachineInit中）
    
    // 切换到虚拟仿真模式进行测试
    MotorControlInterface_SwitchToVirtual();
    
    // 运行电机控制（使用虚拟仿真）
    MotorRun(MOTION_CONTROL_MODE_POSITION, 10, 90.0f, 30.0f, 50.0f, 1.0f, 0.5f);
    
    // 切换到真实硬件模式
    MotorControlInterface_SwitchToReal();
    
    // 运行电机控制（使用真实硬件）
    MotorRun(MOTION_CONTROL_MODE_POSITION, 10, 90.0f, 30.0f, 50.0f, 1.0f, 0.5f);
}
```

### 3. 现有代码无需修改

```c
// 现有的MotorRun调用完全不需要修改
void existing_state_machine_code(void) {
    // 这些调用会自动使用当前设置的模式
    MotorRun(MOTION_CONTROL_MODE_STOP, 0, 7000, 500, 500, 0, 0);
}
```

## 📋 常用API

### 模式控制
```c
// 切换到真实硬件模式
MotorControlInterface_SwitchToReal();

// 切换到虚拟仿真模式
MotorControlInterface_SwitchToVirtual();

// 获取当前模式
ENUM_MOTOR_CONTROL_MODE mode = MotorControlInterface_GetMode();
if (mode == MOTOR_CONTROL_MODE_REAL) {
    // 当前是真实硬件模式
} else {
    // 当前是虚拟仿真模式
}
```

### 直接接口调用
```c
// 直接调用接口（等同于MotorRun）
MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 
                         10,      // CycleTime: 10ms
                         45.0f,   // TargetPosition: 45 degrees
                         20.0f,   // TargetSpeed: 20 deg/s
                         40.0f,   // MaxSpeed: 40 deg/s
                         1.2f,    // KP1
                         0.8f);   // KP2
```

## 🔧 编译配置

### 开发版本编译
1. 确保 `ENABLE_RUNTIME_SWITCH` 已定义
2. 正常编译项目
3. 生成的二进制支持运行时模式切换

### 生产版本编译
1. 注释掉 `#define ENABLE_RUNTIME_SWITCH`
2. 重新编译项目
3. 生成的二进制只包含真实硬件控制代码

## ⚠️ 注意事项

### 开发阶段
- 默认模式是真实硬件模式
- 切换模式后立即生效
- 可以在运行时随时切换模式

### 生产部署
- 必须注释掉 `ENABLE_RUNTIME_SWITCH`
- 重新编译确保不包含测试代码
- 验证二进制文件大小和性能

### 安全考虑
- 生产版本不包含任何虚拟仿真代码
- 无法意外切换到测试模式
- 符合安全关键系统要求

## 🧪 测试建议

### 单元测试
```c
#include "MotorControlTest.h"

void run_tests(void) {
    // 运行所有测试
    MotorControlTest_RunAllTests();
    
    // 获取测试结果
    TestResults results = MotorControlTest_GetResults();
    
    // 检查测试结果
    if (results.testsFailed == 0) {
        // 所有测试通过
    }
}
```

### 集成测试
```c
void integration_test(void) {
    // 测试虚拟模式
    MotorControlInterface_SwitchToVirtual();
    test_motor_functions();
    
    // 测试真实模式
    MotorControlInterface_SwitchToReal();
    test_motor_functions();
}
```

## 📁 相关文件

- `Include/MotorControlConfig.h` - 编译配置
- `Source/Z_Application/StateMachine/externInputInterface.h/c` - 核心接口
- `Source/Z_Application/StateMachine/MotorControlExample.h/c` - 使用示例
- `Source/Z_Application/StateMachine/MotorControlTest.h/c` - 测试代码
- `MotorControlInterface_README.md` - 详细文档
- `架构对比分析报告.md` - 架构分析

## 🆘 故障排除

### 编译错误
- 检查是否正确包含了 `MotorControlConfig.h`
- 确认 `ENABLE_RUNTIME_SWITCH` 的定义状态

### 运行时错误
- 确认在开发模式下才能使用模式切换函数
- 检查是否正确初始化了接口

### 性能问题
- 生产版本应该注释掉 `ENABLE_RUNTIME_SWITCH`
- 验证生成的二进制文件大小

## 📞 支持

如有问题，请参考：
1. `MotorControlInterface_README.md` - 详细技术文档
2. `架构对比分析报告.md` - 架构设计说明
3. 示例代码和测试代码
