# FIXED

Build/Cana_Slave_Data_Process.obj: ../Source/Z_Application/Main/Cana_Slave_Data_Process.c
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/SystemBasic.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Device.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Adc.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DevEmu.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_CpuTimers.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ECan.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ECap.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DMA.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_EPwm.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_EQep.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Gpio.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_I2c.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_McBSP.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_PieCtrl.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_PieVect.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Spi.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Sci.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_SysCtrl.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_XIntrupt.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Xintf.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Examples.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_GlobalPrototypes.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ePwm_defines.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Dma_defines.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_I2C_defines.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/LED.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DefaultISR.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/math.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/linkage.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/string.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdio.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdarg.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlib.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlibf.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/common.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdint.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlib.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlibf.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/phub_can_protocol.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/phub_can.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/pcan_uc_private.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopen.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenMaster_Setup.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenSlave_Setup.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/SystemBasic.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Cana_Slave_Data_Process.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/diskio.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/integer.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/ff.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/ffconf.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/SD.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/sdio_sd.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Communication/ModBusTCP.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/Logger.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Inverter/Inverter.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/FaultCode.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/HistoryError.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/IO/IO.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Manual/Manual.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/MotionControl/MotionControl.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Parameter/ParameterSD.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/CANopenWithMaster/CANopenWithMaster.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Error/Error.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Main/AWSMainFunction.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/ModBus/Modbus.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Parameter/Parameter.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/StateMachine/StateMachine.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/SystemVariable/SystemVariable.h
Build/Cana_Slave_Data_Process.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/File.h

../Source/Z_Application/Main/Cana_Slave_Data_Process.c: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/SystemBasic.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Device.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Adc.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DevEmu.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_CpuTimers.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ECan.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ECap.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DMA.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_EPwm.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_EQep.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Gpio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_I2c.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_McBSP.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_PieCtrl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_PieVect.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Spi.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Sci.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_SysCtrl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_XIntrupt.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Xintf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Examples.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_GlobalPrototypes.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ePwm_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Dma_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_I2C_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/LED.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DefaultISR.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/math.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/linkage.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/string.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdarg.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlib.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlibf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/common.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdint.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlib.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlibf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/phub_can_protocol.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/phub_can.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/pcan_uc_private.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopen.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenMaster_Setup.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenSlave_Setup.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/SystemBasic.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Cana_Slave_Data_Process.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/diskio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/integer.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/ff.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/ffconf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/SD.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/sdio_sd.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Communication/ModBusTCP.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/Logger.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Inverter/Inverter.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/FaultCode.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/HistoryError.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/IO/IO.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Manual/Manual.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/MotionControl/MotionControl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Parameter/ParameterSD.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/CANopenWithMaster/CANopenWithMaster.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Error/Error.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Main/AWSMainFunction.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/ModBus/Modbus.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Parameter/Parameter.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/StateMachine/StateMachine.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/SystemVariable/SystemVariable.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/File.h: 
