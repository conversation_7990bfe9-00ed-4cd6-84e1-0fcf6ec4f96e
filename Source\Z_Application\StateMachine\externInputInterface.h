/**
 * @file externInputInterface.h
 * @brief External input interface for state machine transitions and motor control abstraction
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#ifndef EXTERN_INPUT_INTERFACE_H
#define EXTERN_INPUT_INTERFACE_H

#include "Lib\BackgroundInterface\BackgroundInterface.h"
#include "Lib\MotionControl\MotionControl.h"
#include "MotorControlConfig.h"

/* Motor control mode selection */
typedef enum {
    MOTOR_CONTROL_MODE_REAL,     /* Use real hardware MotionControlRun */
    MOTOR_CONTROL_MODE_VIRTUAL   /* Use virtual simulation */
} ENUM_MOTOR_CONTROL_MODE;

/* External interface functions */
void Input(void);   /* input from outside */
void Output(void);   /* output to outside */

#ifdef ENABLE_RUNTIME_SWITCH
/* Motor control interface functions - only available in development/test builds */
void MotorControlInterface_Init(void);
void MotorControlInterface_SetMode(ENUM_MOTOR_CONTROL_MODE mode);
ENUM_MOTOR_CONTROL_MODE MotorControlInterface_GetMode(void);
void MotorControlInterface_Run(ENUM_MOTION_CONTROL_MODE_SELECT Mode,
                              unsigned int CycleTime,
                              float TargetPosition,
                              float TargetSpeed,
                              float MaxSpeed,
                              float KP1,
                              float KP2);

/* Convenience functions for mode switching */
void MotorControlInterface_SwitchToReal(void);
void MotorControlInterface_SwitchToVirtual(void);
#endif /* ENABLE_RUNTIME_SWITCH */

#endif /* EXTERN_INPUT_INTERFACE_H */
