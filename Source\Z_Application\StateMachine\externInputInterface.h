/**
 * @file externInputInterface.h
 * @brief External input interface for state machine transitions
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#ifndef EXTERN_INPUT_INTERFACE_H
#define EXTERN_INPUT_INTERFACE_H

#include "Lib\BackgroundInterface\BackgroundInterface.h"

void Input(void);   /* input from outside */
void Output(void);   /* output to outside */

#endif /* EXTERN_INPUT_INTERFACE_H */
