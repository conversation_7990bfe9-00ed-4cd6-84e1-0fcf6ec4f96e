#ifndef _CA<PERSON>OPEN_MASTER_H_
#define _CA<PERSON><PERSON>EN_MASTER_H_

typedef enum
{
  <PERSON>NUM_CANOPEN_MASTER_INIT                  = 0,
  <PERSON><PERSON><PERSON>_CANOPEN_MASTER_STOPPED               = 4,
  <PERSON><PERSON><PERSON>_CANOPEN_MASTER_OPERATIONAL           = 5,
  <PERSON><PERSON>M_CANOPEN_MASTER_PRE_OPERATIONAL       = 127

}ENUM_CANOPEN_MASTER_STATUS;

typedef enum
{
    //Send
    <PERSON>NUM_CANOPEN_MASTER_MAIL_BOX_SEND_NMT                                                   = 0,
    <PERSON><PERSON>M_CA<PERSON>OP<PERSON>_MASTER_MAIL_BOX_SEND_SYNC                                                  = 1,
    <PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_MASTER_MAIL_BOX_SEND_RXPDO0_200                                            = 2,
    <PERSON><PERSON><PERSON>_CANOPEN_MASTER_MAIL_BOX_SEND_RXPDO1_300                                            = 3,
    ENU<PERSON>_CANOPEN_MASTER_MAIL_BOX_SEND_RXPDO2_400                                            = 4,
    <PERSON><PERSON><PERSON>_CA<PERSON><PERSON><PERSON>_MASTER_MAIL_BOX_SEND_RXPDO3_500                                            = 5,
    <PERSON><PERSON><PERSON>_CANOPEN_MASTER_MAIL_BOX_SEND_SDO_REQUEST                                           = 6,
    <PERSON><PERSON><PERSON>_CANOPEN_MASTER_MAIL_BOX_SEND_HEARTBEAT_NODEGUARDING_BOOTUP                         = 7,

    //Receive
    ENUM_CANOPEN_MASTER_MAIL_BOX_RECEIVE_SDO_RESPONSE                                       = 16,
    ENUM_CANOPEN_MASTER_MAIL_BOX_RECEIVE_TXPDO0_180                                         = 17,
    ENUM_CANOPEN_MASTER_MAIL_BOX_RECEIVE_TXPDO1_280                                         = 18,
    ENUM_CANOPEN_MASTER_MAIL_BOX_RECEIVE_TXPDO2_380                                         = 19,
    ENUM_CANOPEN_MASTER_MAIL_BOX_RECEIVE_TXPDO3_480                                         = 20,
    ENUM_CANOPEN_MASTER_MAIL_BOX_RECEIVE_HEARTBEAT_NODEGUARDING_BOOTUP                      = 21,
    ENUM_CANOPEN_MASTER_MAIL_BOX_RECEIVE_EMERGENCY                                          = 22

}ENUM_CANOPEN_MASTER_MAIL_BOX_INDEX;

typedef struct
{
    unsigned int Ident;
    unsigned int NoOfBytes;
    unsigned int NewMsg     :1;
    unsigned int Inhibit    :1;
    unsigned char BYTE[8]  ;
}STRUCT_CANOPEN_MASTER_MESSAGE_PROPERTY;


extern volatile STRUCT_CANOPEN_MASTER_MESSAGE_PROPERTY CANopenMasterRXMessage[32];
extern volatile STRUCT_CANOPEN_MASTER_MESSAGE_PROPERTY CANopenMasterTXMessage[32];

extern ENUM_CANOPEN_MASTER_STATUS  CANopenMasterStatus;

extern unsigned char CANopenMasterCommunicationParameterInitDone;

extern void CANOpenMasterStatusInit(void);
extern void CANOpenMasterNMTControl(unsigned int CycleTime);
extern void CANOpenMasterReceivePDOData(void);
extern void CANOpenMasterSendPDOData(unsigned int CycleTime);

#endif
