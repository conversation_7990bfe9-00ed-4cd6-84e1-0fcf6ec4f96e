################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/%.obj: ../Source/Lib/Manual/%.c $(GEN_OPTS) | $(GEN_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: C2000 Compiler'
	"C:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS/bin/cl2000" -v28 -ml -mt --float_support=fpu32 --include_path="D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/User_SEPS50/Include" --advice:performance=all --define=_DEBUG --define=LARGE_MODEL -g --diag_warning=225 --diag_wrap=off --display_error_number --issue_remarks --obj_directory="D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build" --preproc_with_compile --preproc_dependency="Source/Lib/Manual/$(basename $(<F)).d_raw" $(GEN_OPTS__FLAG) "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


