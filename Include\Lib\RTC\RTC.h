#ifndef _RTC_H_
#define _RTC_H_

#define RTC_ADDRESS1                0xd0
#define RTC_ADDRESS2                0xd1

#define RTC_HT1382_OK               0
#define RTC_HT1382_BAD              -1

#define SECOND_REGISTER             0x00
#define MINUTE_REGISTER             0x01
#define HOUR_REGISTER               0x02
#define DATE_REGISTER               0x03
#define MONTH_REGISTER              0x04
#define DAY_REGISTER                0x05
#define YEAR_REGISTER               0x06
#define CONTROL_REGISTER            0x07

#define SECOND_MASK                 0x7F
#define CH_MASK                     0x80
#define MINUTE_MASK                 0x7F
#define HOUR_MASK                   0x3F
#define HOUR_FORMAT_MASK            0x80
#define DATE_MASK                   0x3F
#define MONTH_MASK                  0x1F
#define DAY_MASK                    0x07
#define YEAR_MASK                   0xFF

#define WP_CLOSE_VALUE              0x00
#define WP_OPEN_VALUE               0x80

#define OSCILLATOR_CLOSE_VALUE      0x80
#define OSCILLATOR_OPEN_VALUE       0x00

#define HOUR_FORMAT_12              0x00
#define HOUR_FORMAT_24              0x80

#define RTC_YEAR_MIN_VALU           2000
#define RTC_YEAR_MAX_VALU           2099

#define RTC_READ_DELAY              500

#define MPU_READ_DELAY              100


typedef struct {
    unsigned int Year;       // Year (0 - 99)
    unsigned int Month;      // Month (01 - 12)
    unsigned int Date;       // Date (01 - 31)
    unsigned int Day;        // Day (01 - 07)
    unsigned int Hour;       // Hour (00 - 23)
    unsigned int Minute;    // Minute (00 - 59)
    unsigned int Second;    // Second (00 - 59)
} STRUCT_RTC_DATATIME;


extern STRUCT_RTC_DATATIME RTCDataTime;


extern void RTCInit(void);
extern unsigned char RTCWrite(char RegisterIndex, char WriteDataValue);
extern unsigned char RTCRead(char RegisterIndex, char RegisterMask,unsigned int *ReadDataValue);
extern int RTCReadDateTime(STRUCT_RTC_DATATIME *DateTime);
extern int RTCWriteDateTime(STRUCT_RTC_DATATIME* DateTime);



#endif
