/**
 * @file MotorControlConfig.h
 * @brief Configuration file for motor control architecture selection
 * @note This file controls whether to use runtime switching or direct hardware control
 */

#ifndef MOTOR_CONTROL_CONFIG_H
#define MOTOR_CONTROL_CONFIG_H

/**
 * Motor Control Architecture Configuration
 * 
 * Uncomment the line below to enable runtime switching between real and virtual motor control.
 * Comment it out for production builds to use direct hardware control only.
 * 
 * ENABLE_RUNTIME_SWITCH:
 * - Defined: Development/Test version with runtime switching capability
 * - Not defined: Production version with direct hardware control only
 */

/* Development/Test Configuration - Enable runtime switching */
#define ENABLE_RUNTIME_SWITCH

/* Production Configuration - Comment out the line above for production builds */
// #undef ENABLE_RUNTIME_SWITCH

/**
 * Configuration Summary:
 * 
 * Development/Test Version (ENABLE_RUNTIME_SWITCH defined):
 * ✓ Runtime switching between real and virtual control
 * ✓ Convenient for testing and debugging
 * ✓ Single binary supports both modes
 * ✗ Slightly larger binary size
 * ✗ Minor runtime overhead
 * 
 * Production Version (ENABLE_RUNTIME_SWITCH not defined):
 * ✓ Direct hardware control, maximum performance
 * ✓ Smaller binary size
 * ✓ No runtime overhead
 * ✓ No test code in production binary
 * ✗ Single mode only (real hardware)
 */

#endif /* MOTOR_CONTROL_CONFIG_H */
