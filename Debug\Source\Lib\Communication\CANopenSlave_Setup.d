# FIXED

Build/CANopenSlave_Setup.obj: ../Source/Lib/Communication/CANopenSlave_Setup.c
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenSlave_Setup.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Lib/BackgroundInterface/BackgroundInterface.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Device.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Adc.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_DevEmu.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_CpuTimers.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_ECan.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_ECap.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_DMA.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_EPwm.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_EQep.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Gpio.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_I2c.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_McBSP.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_PieCtrl.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_PieVect.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Spi.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Sci.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_SysCtrl.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_XIntrupt.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Xintf.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Examples.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_GlobalPrototypes.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_ePwm_defines.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Dma_defines.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_I2C_defines.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_DefaultISR.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/linkage.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdarg.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Lib/BackgroundInterface/BackgroundInterface.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/integer.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/math.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/string.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdio.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdlib.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdlibf.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdint.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Communication/ModBusTCP.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/Logger.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Inverter/Inverter.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/FaultCode.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/HistoryError.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/IO/IO.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Manual/Manual.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/MotionControl/MotionControl.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Parameter/ParameterSD.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/diskio.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/ffconf.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/ff.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/SD.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/sdio_sd.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/File.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/CANopenWithMaster/CANopenWithMaster.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Error/Error.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Main/AWSMainFunction.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/ModBus/Modbus.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Parameter/Parameter.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/StateMachine/StateMachine.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/SystemVariable/SystemVariable.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopen.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenMaster_Setup.h
Build/CANopenSlave_Setup.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenSlave_Setup.h

../Source/Lib/Communication/CANopenSlave_Setup.c: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenSlave_Setup.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Lib/BackgroundInterface/BackgroundInterface.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Device.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Adc.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_DevEmu.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_CpuTimers.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_ECan.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_ECap.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_DMA.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_EPwm.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_EQep.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Gpio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_I2c.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_McBSP.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_PieCtrl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_PieVect.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Spi.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Sci.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_SysCtrl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_XIntrupt.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Xintf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Examples.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_GlobalPrototypes.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_ePwm_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_Dma_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_I2C_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/DSP2833x_DefaultISR.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/linkage.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdarg.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Lib/BackgroundInterface/BackgroundInterface.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/integer.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/math.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/string.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdlib.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdlibf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/stdint.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Communication/ModBusTCP.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/Logger.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Inverter/Inverter.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/FaultCode.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/HistoryError.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/IO/IO.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Manual/Manual.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/MotionControl/MotionControl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Parameter/ParameterSD.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/diskio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/ffconf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/ff.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/SD.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/sdio_sd.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/SDCard/File.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/CANopenWithMaster/CANopenWithMaster.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Error/Error.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Main/AWSMainFunction.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/ModBus/Modbus.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Parameter/Parameter.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/StateMachine/StateMachine.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/SystemVariable/SystemVariable.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopen.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenMaster_Setup.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenSlave_Setup.h: 
