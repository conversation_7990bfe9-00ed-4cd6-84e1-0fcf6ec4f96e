#ifndef _SYSTEM_VARIABLE_H_
#define _SYSTEM_VARIABLE_H_


typedef struct
{
    unsigned char ActualValue;
    unsigned char DelaylValue;
    unsigned char DelayTiming;
    unsigned char ActualValueLast;

} STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY;

typedef struct
{
    STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY SwitchA;
    STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY SwitchB;
    STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY SwitchC;

    STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY DI[18];

} STRUCT_SYSTEM_VARIABLE_DI;

extern STRUCT_SYSTEM_VARIABLE_DI SystemVariablDI;

extern unsigned int OD_CANopenSlave_Controlword_Value;

extern void SystemVariableInit(void);
extern void SystemVariableRun10MS(unsigned int CycleTime);
extern void SystemVariableRun100MS(unsigned int CycleTime);
extern void SystemVariableRun1S(unsigned int CycleTime);
extern void SystemVariableIOTriggerDelay(unsigned char ActualSignal, STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY *pIO,  unsigned int CycleTime, unsigned int DelayTime);


#endif
