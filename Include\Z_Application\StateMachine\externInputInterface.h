/**
 * @file externInputInterface.h
 * @brief External input interface for state machine transitions
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#ifndef EXTERN_INPUT_INTERFACE_H
#define EXTERN_INPUT_INTERFACE_H

#include "stateMachine.h"

/* External input interface functions - Big CamelCase naming */
void InputTf18(void);   /* PowerOn->Manual */
void InputTf13(void);   /* PowerOn->Standby */
void InputTf36(void);   /* Standby->PreNormal */
void InputTf64(void);   /* PreNormal->Normal */
void InputTf65(void);   /* PreNormal->VortexRun */
void InputTf910(void);  /* VortexRun->VortexBrake */
void InputTf105(void);  /* VortexBrake->Emergency */
void InputTf45(void);   /* Normal->Emergency */
void InputTf57(void);   /* Emergency->BackupTest */
void InputTf73(void);   /* BackupTest->Standby */
void InputTf83(void);   /* Manual->Standby */
void InputTf53(void);   /* Emergency->Standby */
void InputTf95(void);   /* VortexRun->Emergency */

#endif /* EXTERN_INPUT_INTERFACE_H */
