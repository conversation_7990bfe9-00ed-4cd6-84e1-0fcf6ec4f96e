# FIXED

Build/SystemBasic.obj: ../Source/Lib/Basic/SystemBasic.c
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/SystemBasic.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Device.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Adc.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DevEmu.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_CpuTimers.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ECan.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ECap.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DMA.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_EPwm.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_EQep.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Gpio.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_I2c.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_McBSP.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_PieCtrl.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_PieVect.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Spi.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Sci.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_SysCtrl.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_XIntrupt.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Xintf.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Examples.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_GlobalPrototypes.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ePwm_defines.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Dma_defines.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_I2C_defines.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DefaultISR.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/math.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/linkage.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/string.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdio.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdarg.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlib.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlibf.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdint.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopen.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenMaster_Setup.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenSlave_Setup.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/SystemBasic.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/diskio.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/integer.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/ff.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/ffconf.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/SD.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/sdio_sd.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Communication/ModBusTCP.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/Logger.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Inverter/Inverter.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/FaultCode.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/HistoryError.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/IO/IO.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Manual/Manual.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/MotionControl/MotionControl.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Parameter/ParameterSD.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/CANopenWithMaster/CANopenWithMaster.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Error/Error.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Main/AWSMainFunction.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/ModBus/Modbus.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Parameter/Parameter.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/StateMachine/StateMachine.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/SystemVariable/SystemVariable.h
Build/SystemBasic.obj: D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/File.h

../Source/Lib/Basic/SystemBasic.c: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/SystemBasic.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Device.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Adc.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DevEmu.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_CpuTimers.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ECan.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ECap.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DMA.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_EPwm.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_EQep.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Gpio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_I2c.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_McBSP.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_PieCtrl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_PieVect.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Spi.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Sci.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_SysCtrl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_XIntrupt.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Xintf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Examples.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_GlobalPrototypes.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_ePwm_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_Dma_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_I2C_defines.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/DSP2833x_DefaultISR.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/math.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/linkage.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/string.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdarg.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlib.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdlibf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/stdint.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopen.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenMaster_Setup.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/CANopenSlave_Setup.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Basic/SystemBasic.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/diskio.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/integer.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/ff.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/ffconf.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/SD.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/sdio_sd.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Communication/ModBusTCP.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/Logger.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Inverter/Inverter.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/FaultCode.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Fault/HistoryError.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/IO/IO.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Manual/Manual.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/MotionControl/MotionControl.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/./Lib/Parameter/ParameterSD.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/CANopenWithMaster/CANopenWithMaster.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Error/Error.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Main/AWSMainFunction.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/ModBus/Modbus.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/Parameter/Parameter.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/StateMachine/StateMachine.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/Z_Application/SystemVariable/SystemVariable.h: 
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/ZC_Pitch_Driver/Include/File.h: 
