#ifndef PARAMETER_H_
#define PARAMETER_H_

#define UserParameter (void(*)(void))0x328000

typedef enum
{
    enum_OD_CANopenSlave_TSet,
    enum_OD_CANopenSlave_GearRatioMotorRevolutions_B,

    enum_ParameterListEnd

} ENUM_PARAMETER_LIST;

typedef struct
{
    unsigned int PatameterList[4000];
} STRUCT_PARAMETER_LIST;

extern STRUCT_PARAMETER_WR_COMMAND ParameterWRCommand[PARAMETER_EXTERNAL_WR_MAX_SIZE];


extern void ParameterInit(void);
extern void ParameterRun(void);

#endif


