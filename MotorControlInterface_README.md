# 电机控制接口抽象层 (Motor Control Interface Abstraction Layer)

## 概述

本项目实现了一个**混合架构**的电机控制接口抽象层，通过 `externInputInterface` 作为中间层，结合编译时和运行时切换的优势，提供了灵活的开发测试环境和高效的生产部署方案。

## 🏗️ 混合架构设计

### 开发/测试版本 (`ENABLE_RUNTIME_SWITCH` 定义)
- ✅ 支持运行时在真实硬件和虚拟仿真之间切换
- ✅ 一次编译，多种模式测试
- ✅ 便于调试和对比测试

### 生产版本 (`ENABLE_RUNTIME_SWITCH` 未定义)
- ✅ 直接调用真实硬件控制，性能最优
- ✅ 不包含测试代码，二进制文件最小
- ✅ 零运行时开销，符合安全关键系统要求

## 主要改进

### 原来的实现方式
```c
void MotorRun(...) {
#ifdef DEBUG_USE
    // 虚拟仿真代码
    gSTservoSimState.dPositionCommand = ...;
    SimulationStep(&gSTservoSimState);
#else
    // 真实硬件代码
    MotionControlRun(Mode, CycleTime, ...);
#endif
}
```

### 新的实现方式
```c
void MotorRun(...) {
    // 统一调用接口抽象层
    MotorControlInterface_Run(Mode, CycleTime, TargetPosition, TargetSpeed, MaxSpeed, KP1, KP2);
}
```

## 核心文件

### 1. externInputInterface.h
- 定义了电机控制模式枚举 `ENUM_MOTOR_CONTROL_MODE`
- 声明了电机控制接口函数
- 提供便捷的模式切换函数

### 2. externInputInterface.c
- 实现了电机控制接口的核心逻辑
- 根据当前模式选择调用真实硬件或虚拟仿真
- 提供运行时模式切换功能

### 3. stateMachine.c (修改)
- `MotorRun` 函数现在调用统一的接口抽象层
- `StateMachineInit` 函数中初始化电机控制接口

## API 接口

### 初始化函数
```c
void MotorControlInterface_Init(void);
```
初始化电机控制接口，默认设置为真实硬件模式。

### 模式设置函数
```c
void MotorControlInterface_SetMode(ENUM_MOTOR_CONTROL_MODE mode);
ENUM_MOTOR_CONTROL_MODE MotorControlInterface_GetMode(void);
```
设置和获取当前的电机控制模式。

### 便捷切换函数
```c
void MotorControlInterface_SwitchToReal(void);    // 切换到真实硬件模式
void MotorControlInterface_SwitchToVirtual(void); // 切换到虚拟仿真模式
```

### 核心运行函数
```c
void MotorControlInterface_Run(ENUM_MOTION_CONTROL_MODE_SELECT Mode, 
                              unsigned int CycleTime,
                              float TargetPosition, 
                              float TargetSpeed,
                              float MaxSpeed, 
                              float KP1, 
                              float KP2);
```
根据当前设置的模式，调用相应的电机控制函数。

## 使用方法

### 基本使用
```c
// 1. 初始化接口
MotorControlInterface_Init();

// 2. 设置为真实硬件模式
MotorControlInterface_SwitchToReal();

// 3. 运行电机控制
MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 10, 90.0f, 30.0f, 50.0f, 1.0f, 0.5f);

// 4. 切换到虚拟仿真模式
MotorControlInterface_SwitchToVirtual();

// 5. 继续运行电机控制（现在使用虚拟仿真）
MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 10, 45.0f, 20.0f, 40.0f, 1.2f, 0.8f);
```

### 与现有代码集成
现有的 `MotorRun` 函数调用无需修改，只需要在适当的时候设置控制模式：

```c
// 设置模式
MotorControlInterface_SwitchToVirtual();

// 现有代码无需修改
MotorRun(MOTION_CONTROL_MODE_STOP, 0, 7000, 500, 500, 0, 0);
```

### 条件切换示例
```c
void SetMotorControlMode(bool useVirtualMode) {
    if (useVirtualMode) {
        MotorControlInterface_SwitchToVirtual();
    } else {
        MotorControlInterface_SwitchToReal();
    }
}
```

## 优势

1. **运行时切换**: 不需要重新编译就可以切换控制模式
2. **代码复用**: 现有的状态机代码无需修改
3. **测试友好**: 可以在同一个程序中测试两种模式
4. **维护性好**: 统一的接口，便于维护和扩展
5. **向后兼容**: 保持了原有的 `MotorRun` 函数接口

## 示例代码

详细的使用示例请参考：
- `MotorControlExample.c` - 包含各种使用场景的示例
- `MotorControlExample.h` - 示例函数的声明

## 注意事项

1. 在使用虚拟仿真模式时，确保 `gSTservoSimState` 已正确初始化
2. 模式切换是立即生效的，下次调用 `MotorControlInterface_Run` 时会使用新模式
3. 系统启动时默认使用真实硬件模式
4. 虚拟仿真模式依赖于 `ServoSimCore` 模块的正确配置

## 扩展性

该接口设计具有良好的扩展性，未来可以轻松添加：
- 新的控制模式（如混合模式）
- 更多的参数配置选项
- 运行时诊断和监控功能
- 模式切换的回调函数
