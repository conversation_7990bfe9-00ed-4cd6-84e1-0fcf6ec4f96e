################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Source/Lib/Fault/FaultCode.c \
../Source/Lib/Fault/HistoryError.c \
../Source/Lib/Fault/Logger.c 

C_DEPS += \
./Source/Lib/Fault/FaultCode.d \
./Source/Lib/Fault/HistoryError.d \
./Source/Lib/Fault/Logger.d 

OBJS += \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/FaultCode.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/HistoryError.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/Logger.obj 

OBJS__QUOTED += \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\FaultCode.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\HistoryError.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\Logger.obj" 

C_DEPS__QUOTED += \
"Source\Lib\Fault\FaultCode.d" \
"Source\Lib\Fault\HistoryError.d" \
"Source\Lib\Fault\Logger.d" 

C_SRCS__QUOTED += \
"../Source/Lib/Fault/FaultCode.c" \
"../Source/Lib/Fault/HistoryError.c" \
"../Source/Lib/Fault/Logger.c" 


