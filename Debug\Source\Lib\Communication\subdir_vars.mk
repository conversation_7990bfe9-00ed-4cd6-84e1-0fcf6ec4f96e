################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Source/Lib/Communication/CANopenMaster.c \
../Source/Lib/Communication/CANopenSlave.c \
../Source/Lib/Communication/ModBusTCP.c 

C_DEPS += \
./Source/Lib/Communication/CANopenMaster.d \
./Source/Lib/Communication/CANopenSlave.d \
./Source/Lib/Communication/ModBusTCP.d 

OBJS += \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/CANopenMaster.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/CANopenSlave.obj \
D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/SEPS50/Debug/Build/ModBusTCP.obj 

OBJS__QUOTED += \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\CANopenMaster.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\CANopenSlave.obj" \
"D:\work\CCS\workspace_v8\DSP2833x_examples\ZONCN\Shanghai_Electric\With_PLC\SEPS50\Debug\Build\ModBusTCP.obj" 

C_DEPS__QUOTED += \
"Source\Lib\Communication\CANopenMaster.d" \
"Source\Lib\Communication\CANopenSlave.d" \
"Source\Lib\Communication\ModBusTCP.d" 

C_SRCS__QUOTED += \
"../Source/Lib/Communication/CANopenMaster.c" \
"../Source/Lib/Communication/CANopenSlave.c" \
"../Source/Lib/Communication/ModBusTCP.c" 


