#ifndef _FILE_H_
#define _FILE_H_

#include "Lib\BackgroundInterface\BackgroundInterface.h"

typedef enum
{
    ENUM_FILE_TYPE_IDEL_FILE 					    = 0,
    ENUM_FILE_TYPE_PARAMETER_FILE 				    = 1,
    ENUM_FILE_TYPE_FAULE_CODE_PROPERTY_FILE         = 2,
    ENUM_FILE_TYPE_HISTORY_ERROR_WRITE_FILE 	    = 3,
    ENUM_FILE_TYPE_HISTORY_ERROR_READ_FILE 		    = 4,
    ENUM_FILE_TYPE_LOGGER_WRITE_FILE 				= 5,
    ENUM_FILE_TYPE_LOGGER_READ_FILE                 = 6

}ENUM_FILE_HANDLE_TYPE;

extern void FileSDInit(void);
extern FRESULT FileSDWrite(ENUM_FILE_HANDLE_TYPE FileType,char *FilePath, unsigned char *FileContent, unsigned long *FilePositionStart, unsigned int *FileLength);
extern FRESULT FileSDRead(ENUM_FILE_HANDLE_TYPE FileType, char *FilePath, unsigned char *FileContent, unsigned int *FileLength);
extern FRESULT FileSDFormat(void);

extern unsigned int FileStringByteLength(unsigned char *str);
extern void FileFloatToChar(float Data, char *StrBuffer);
extern void FileIntToChar(int Data, char *StrBuffer);
extern void FileLongToChar(long Data, char *StrBuffer);
extern FRESULT FileCharToFloat(char *str, float *Data);
extern FRESULT FileCharToInt(char *str, int *Data);
extern FRESULT FileCharToLong(char *str, long *Data);
extern FRESULT FileParameterCharacterClassify(char * pName, char * pValue, char * pReadCharacter, unsigned int FileLen);

extern volatile unsigned char SDSPIBusy;

extern unsigned char SDFormatCommondDone;

#endif
