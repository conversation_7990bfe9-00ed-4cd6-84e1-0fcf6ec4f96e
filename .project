<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>User_SHE_WITHOUT_PLCAugment</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.ti.ccstudio.core.ccsNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>Debug/User_SEPS50.out</name>
			<type>1</type>
			<locationURI>EXTERNAL_BUILD_ARTIFACT</locationURI>
		</link>
	</linkedResources>
	<variableList>
		<variable>
			<name>SRC_ROOT</name>
			<value>$%7BPARENT-3-PROJECT_LOC%7D</value>
		</variable>
	</variableList>
</projectDescription>
