/**
 * @file MotorControlTest.c
 * @brief Test functions for the motor control interface
 * @note This file provides test functions to verify the motor control abstraction layer
 */

#include "externInputInterface.h"
#include "MotorControlExample.h"
#include "Lib\BackgroundInterface\BackgroundInterface.h"

/* Test result structure */
typedef struct {
    unsigned int testsPassed;
    unsigned int testsFailed;
    unsigned int totalTests;
} TestResults;

static TestResults gTestResults = {0, 0, 0};

/* Helper function to check test results */
static void CheckTest(const char* testName, bool condition) {
    gTestResults.totalTests++;
    if (condition) {
        gTestResults.testsPassed++;
        /* Test passed - could add logging here */
    } else {
        gTestResults.testsFailed++;
        /* Test failed - could add logging here */
    }
}

/**
 * @brief Test motor control interface initialization
 */
void MotorControlTest_Initialization(void) {
    ENUM_MOTOR_CONTROL_MODE mode;
    
    /* Test 1: Initialize interface */
    MotorControlInterface_Init();
    
    /* Test 2: Check default mode is REAL */
    mode = MotorControlInterface_GetMode();
    CheckTest("Default mode is REAL", mode == MOTOR_CONTROL_MODE_REAL);
}

/**
 * @brief Test mode switching functionality
 */
void MotorControlTest_ModeSwitching(void) {
    ENUM_MOTOR_CONTROL_MODE mode;
    
    /* Test 1: Switch to virtual mode */
    MotorControlInterface_SwitchToVirtual();
    mode = MotorControlInterface_GetMode();
    CheckTest("Switch to virtual mode", mode == MOTOR_CONTROL_MODE_VIRTUAL);
    
    /* Test 2: Switch to real mode */
    MotorControlInterface_SwitchToReal();
    mode = MotorControlInterface_GetMode();
    CheckTest("Switch to real mode", mode == MOTOR_CONTROL_MODE_REAL);
    
    /* Test 3: Direct mode setting */
    MotorControlInterface_SetMode(MOTOR_CONTROL_MODE_VIRTUAL);
    mode = MotorControlInterface_GetMode();
    CheckTest("Direct mode setting", mode == MOTOR_CONTROL_MODE_VIRTUAL);
}

/**
 * @brief Test motor control run function with different modes
 */
void MotorControlTest_RunFunction(void) {
    /* Test 1: Run in real mode */
    MotorControlInterface_SwitchToReal();
    
    /* This should not crash - actual hardware testing would require real hardware */
    MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 
                             10,      /* CycleTime: 10ms */
                             0.0f,    /* TargetPosition: 0 degrees */
                             10.0f,   /* TargetSpeed: 10 deg/s */
                             20.0f,   /* MaxSpeed: 20 deg/s */
                             1.0f,    /* KP1 */
                             0.5f);   /* KP2 */
    
    CheckTest("Real mode run function", 1); /* Assume success if no crash */
    
    /* Test 2: Run in virtual mode */
    MotorControlInterface_SwitchToVirtual();
    
    MotorControlInterface_Run(MOTION_CONTROL_MODE_POSITION, 
                             10,      /* CycleTime: 10ms */
                             45.0f,   /* TargetPosition: 45 degrees */
                             15.0f,   /* TargetSpeed: 15 deg/s */
                             30.0f,   /* MaxSpeed: 30 deg/s */
                             1.2f,    /* KP1 */
                             0.8f);   /* KP2 */
    
    CheckTest("Virtual mode run function", 1); /* Assume success if no crash */
}

/**
 * @brief Test integration with existing MotorRun function
 */
void MotorControlTest_MotorRunIntegration(void) {
    /* Test 1: MotorRun with real mode */
    MotorControlInterface_SwitchToReal();
    
    /* Call the existing MotorRun function */
    MotorRun(MOTION_CONTROL_MODE_STOP, 
             0,       /* CycleTime */
             0.0f,    /* TargetPosition */
             0.0f,    /* TargetSpeed */
             0.0f,    /* MaxSpeed */
             0.0f,    /* KP1 */
             0.0f);   /* KP2 */
    
    CheckTest("MotorRun integration - real mode", 1);
    
    /* Test 2: MotorRun with virtual mode */
    MotorControlInterface_SwitchToVirtual();
    
    MotorRun(MOTION_CONTROL_MODE_STOP, 
             0,       /* CycleTime */
             0.0f,    /* TargetPosition */
             0.0f,    /* TargetSpeed */
             0.0f,    /* MaxSpeed */
             0.0f,    /* KP1 */
             0.0f);   /* KP2 */
    
    CheckTest("MotorRun integration - virtual mode", 1);
}

/**
 * @brief Run all motor control tests
 */
void MotorControlTest_RunAllTests(void) {
    /* Reset test results */
    gTestResults.testsPassed = 0;
    gTestResults.testsFailed = 0;
    gTestResults.totalTests = 0;
    
    /* Run all test suites */
    MotorControlTest_Initialization();
    MotorControlTest_ModeSwitching();
    MotorControlTest_RunFunction();
    MotorControlTest_MotorRunIntegration();
    
    /* Test results are stored in gTestResults for external access */
}

/**
 * @brief Get test results
 */
TestResults MotorControlTest_GetResults(void) {
    return gTestResults;
}

/**
 * @brief Simple test runner with basic output
 */
void MotorControlTest_SimpleRunner(void) {
    /* Run all tests */
    MotorControlTest_RunAllTests();
    
    /* Basic result checking - in a real system you might want to log these */
    if (gTestResults.testsFailed == 0) {
        /* All tests passed */
        /* Could set a success flag or LED here */
    } else {
        /* Some tests failed */
        /* Could set an error flag or LED here */
    }
}
