/**
 * @file MotorControlExample.h
 * @brief Header file for motor control interface examples
 * @note This file provides example functions for using the motor control abstraction layer
 */

#ifndef MOTOR_CONTROL_EXAMPLE_H
#define MOTOR_CONTROL_EXAMPLE_H

#include "externInputInterface.h"

/* Example function prototypes */
void MotorControlExample_Demo(void);
void MotorControlExample_TestModes(void);
void MotorControlExample_ConditionalSwitching(void);
void MotorControlExample_StateMachineIntegration(void);

#endif /* MOTOR_CONTROL_EXAMPLE_H */
