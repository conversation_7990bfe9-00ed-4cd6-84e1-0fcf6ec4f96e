################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

CG_TOOL_ROOT := D:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS

GEN_OPTS__FLAG := 
GEN_CMDS__FLAG := 

ORDERED_OBJS += \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/DSP2833x_ADC_cal.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/DSP2833x_CodeStartBranch.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/DSP2833x_usDelay.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/Error.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/AWSMainFunction.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/ModbusRTU.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/ModbusTCP.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/ObjectDictionary.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/Parameter.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/ServoSimCore.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/externInputInterface.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/stateMachine.obj" \
"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/SystemVariable.obj" \
"../F28335.cmd" \
"../User_SHE_WITHOUT_PLC_Lib.lib" \
"../Source/Lib/Basic/DSP2833x_Headers_nonBIOS.cmd" \
$(GEN_CMDS__FLAG) \
-llibc.a \
-lrts2800_fpu32.lib \

-include ../makefile.init

RM := DEL /F
RMDIR := RMDIR /S/Q

# All of the sources participating in the build are defined here
-include sources.mk
-include subdir_vars.mk
-include Source/Lib/Basic/subdir_vars.mk
-include Source/Z_Application/Error/subdir_vars.mk
-include Source/Z_Application/Main/subdir_vars.mk
-include Source/Z_Application/ModBus/subdir_vars.mk
-include Source/Z_Application/ObjectDictionary/subdir_vars.mk
-include Source/Z_Application/Parameter/subdir_vars.mk
-include Source/Z_Application/StateMachine/subdir_vars.mk
-include Source/Z_Application/SystemVariable/subdir_vars.mk
-include subdir_rules.mk
-include Source/Lib/Basic/subdir_rules.mk
-include Source/Z_Application/Error/subdir_rules.mk
-include Source/Z_Application/Main/subdir_rules.mk
-include Source/Z_Application/ModBus/subdir_rules.mk
-include Source/Z_Application/ObjectDictionary/subdir_rules.mk
-include Source/Z_Application/Parameter/subdir_rules.mk
-include Source/Z_Application/StateMachine/subdir_rules.mk
-include Source/Z_Application/SystemVariable/subdir_rules.mk
-include objects.mk

ifneq ($(MAKECMDGOALS),clean)
ifneq ($(strip $(C55_DEPS)),)
-include $(C55_DEPS)
endif
ifneq ($(strip $(C_UPPER_DEPS)),)
-include $(C_UPPER_DEPS)
endif
ifneq ($(strip $(S67_DEPS)),)
-include $(S67_DEPS)
endif
ifneq ($(strip $(S62_DEPS)),)
-include $(S62_DEPS)
endif
ifneq ($(strip $(S_DEPS)),)
-include $(S_DEPS)
endif
ifneq ($(strip $(OPT_DEPS)),)
-include $(OPT_DEPS)
endif
ifneq ($(strip $(C??_DEPS)),)
-include $(C??_DEPS)
endif
ifneq ($(strip $(ASM_UPPER_DEPS)),)
-include $(ASM_UPPER_DEPS)
endif
ifneq ($(strip $(S??_DEPS)),)
-include $(S??_DEPS)
endif
ifneq ($(strip $(C64_DEPS)),)
-include $(C64_DEPS)
endif
ifneq ($(strip $(CXX_DEPS)),)
-include $(CXX_DEPS)
endif
ifneq ($(strip $(S64_DEPS)),)
-include $(S64_DEPS)
endif
ifneq ($(strip $(INO_DEPS)),)
-include $(INO_DEPS)
endif
ifneq ($(strip $(CLA_DEPS)),)
-include $(CLA_DEPS)
endif
ifneq ($(strip $(S55_DEPS)),)
-include $(S55_DEPS)
endif
ifneq ($(strip $(SV7A_DEPS)),)
-include $(SV7A_DEPS)
endif
ifneq ($(strip $(C62_DEPS)),)
-include $(C62_DEPS)
endif
ifneq ($(strip $(C67_DEPS)),)
-include $(C67_DEPS)
endif
ifneq ($(strip $(PDE_DEPS)),)
-include $(PDE_DEPS)
endif
ifneq ($(strip $(K_DEPS)),)
-include $(K_DEPS)
endif
ifneq ($(strip $(C_DEPS)),)
-include $(C_DEPS)
endif
ifneq ($(strip $(CC_DEPS)),)
-include $(CC_DEPS)
endif
ifneq ($(strip $(C++_DEPS)),)
-include $(C++_DEPS)
endif
ifneq ($(strip $(C43_DEPS)),)
-include $(C43_DEPS)
endif
ifneq ($(strip $(S43_DEPS)),)
-include $(S43_DEPS)
endif
ifneq ($(strip $(ASM_DEPS)),)
-include $(ASM_DEPS)
endif
ifneq ($(strip $(S_UPPER_DEPS)),)
-include $(S_UPPER_DEPS)
endif
ifneq ($(strip $(CPP_DEPS)),)
-include $(CPP_DEPS)
endif
ifneq ($(strip $(SA_DEPS)),)
-include $(SA_DEPS)
endif
endif

-include ../makefile.defs

# Add inputs and outputs from these tool invocations to the build variables 
EXE_OUTPUTS += \
D:/CCS_Program/User_SHE_WITHOUT_PLCAugment/../../../Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.out \

EXE_OUTPUTS__QUOTED += \
"D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\User_SEPS50.out" \

BIN_OUTPUTS += \
D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.hex \

BIN_OUTPUTS__QUOTED += \
"D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\User_SEPS50.hex" \


# All Target
all: D:/CCS_Program/User_SHE_WITHOUT_PLCAugment/../../../Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.out secondary-outputs

# Tool invocations
D:/CCS_Program/User_SHE_WITHOUT_PLCAugment/../../../Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.out: $(OBJS) $(CMD_SRCS) $(LIB_SRCS) $(GEN_CMDS)
	@echo 'Building target: "$@"'
	@echo 'Invoking: C2000 Linker'
	"D:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS/bin/cl2000" -v28 -ml -mt --float_support=fpu32 --advice:performance=all --define=_DEBUG --define=LARGE_MODEL -g --diag_warning=225 --diag_wrap=off --display_error_number --issue_remarks --obj_directory="D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build" -z -m"D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.map" --heap_size=0x400 --stack_size=0x400 --warn_sections -i"D:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS/lib" -i"D:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS/include" -i"D:/CCS_Program/User_SHE_WITHOUT_PLCAugment" -i"D:/CCS_Program/User_SHE_WITHOUT_PLCAugment" --reread_libs --diag_wrap=off --display_error_number --xml_link_info="User_SHE_WITHOUT_PLCAugment_linkInfo.xml" --rom_model -o "D:/CCS_Program/User_SHE_WITHOUT_PLCAugment/../../../Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.out" $(ORDERED_OBJS)
	@echo 'Finished building target: "$@"'
	@echo ' '

D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.hex: $(EXE_OUTPUTS)
	@echo 'Building files: $(strip $(EXE_OUTPUTS__QUOTED))'
	@echo 'Invoking: C2000 Hex Utility'
	"D:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS/bin/hex2000" --array -o "D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.hex" $(EXE_OUTPUTS__QUOTED)
	@echo 'Finished building: $(strip $(EXE_OUTPUTS__QUOTED))'
	@echo ' '
	@$(MAKE) --no-print-directory post-build

# Other Targets
clean:
	-$(RM) $(BIN_OUTPUTS__QUOTED)$(EXE_OUTPUTS__QUOTED)
	-$(RM) "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\DSP2833x_ADC_cal.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\DSP2833x_CodeStartBranch.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\DSP2833x_usDelay.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\Error.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\AWSMainFunction.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\ModbusRTU.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\ModbusTCP.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\ObjectDictionary.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\Parameter.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\ServoSimCore.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\externInputInterface.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\stateMachine.obj" "D:\Shanghai_Electric\With_PLC\User_SEPS50\Debug\Build\SystemVariable.obj" 
	-$(RM) "Source\Z_Application\Error\Error.d" "Source\Z_Application\Main\AWSMainFunction.d" "Source\Z_Application\ModBus\ModbusRTU.d" "Source\Z_Application\ModBus\ModbusTCP.d" "Source\Z_Application\ObjectDictionary\ObjectDictionary.d" "Source\Z_Application\Parameter\Parameter.d" "Source\Z_Application\StateMachine\ServoSimCore.d" "Source\Z_Application\StateMachine\externInputInterface.d" "Source\Z_Application\StateMachine\stateMachine.d" "Source\Z_Application\SystemVariable\SystemVariable.d" 
	-$(RM) "Source\Lib\Basic\DSP2833x_ADC_cal.d" "Source\Lib\Basic\DSP2833x_CodeStartBranch.d" "Source\Lib\Basic\DSP2833x_usDelay.d" 
	-@echo 'Finished clean'
	-@echo ' '

post-build:
	-"D:/ti/ccsv8/utils/tiobj2bin/tiobj2bin.bat" "D:/CCS_Program/User_SHE_WITHOUT_PLCAugment/../../../Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.out" "D:/CCS_Program/User_SHE_WITHOUT_PLCAugment/../../../Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.bin" "D:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS/bin/ofd2000.exe" "D:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS/bin/hex2000.exe" "D:/ti/ccsv8/utils/tiobj2bin/mkhex4bin.exe"
	-@echo ' '

secondary-outputs: $(BIN_OUTPUTS)

.PHONY: all clean dependents
.SECONDARY:

-include ../makefile.targets

