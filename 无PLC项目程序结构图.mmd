%% 程序结构图
classDiagram
    %% 主控制模块
    class AWSMainFunction {
        +main(): void
        +AWSTaskCycle2MS(): void
        +AWSTaskCycle10MS(): void
        +AWSTaskCycle100MS(): void
        +AWSSpecialVariableAssignment(): void
        -AWS.Write.GearRatio: float
        -AWS.Write.SSIGearRatio: float
    }

    %% 状态机模块
    class StateMachine {
        +StateMachineInit(): void
        +StateMachineRun(unsigned int): void
        +StateMachine_Status_Switch(...): void
        +StateMachine_Status_Init(...): void
        +StateMachine_Status_Stop(...): void
        +StateMachine_Status_Velocity(...): void
        +StateMachine_Status_Position(...): void
        +StateMachine_Status_EmergencyRun(...): void
        -stcStateMachine: STRUCT_STATE_MACHINE_INFORMATION
        -StatemachineRunToEmergencyFlag: bool
    }

    %% 系统变量模块
    class SystemVariable {
        +SystemVariableInit(): void
        +SystemVariableRun10MS(unsigned int): void
        +SystemVariableRun100MS(unsigned int): void
        +SystemVariableRun1S(unsigned int): void
        -SystemVariablDI: STRUCT_SYSTEM_VARIABLE_DI
        -OD_CANopenSlave_Controlword_Value: unsigned int
    }

    %% 运动控制模块
    class MotionControl {
        +MotionControlRun(...): void
        -AWS.Write.GearRatio
        -AWS.Write.SSIGearRatio
    }

    %% 故障管理模块
    class FaultCode {
        +FaultCodeGetStatus(): ENUM_FC_MOTION_STATUS
        +FaultCodeSet(...): void
    }

    %% CANopen从站模块
    class CANopenSlave {
        +CANOpenSlaveFindEntryInOD(...): STRUCT_OBJECT_DICTIONARY*
        -OD_CANopenSlave_ProfileVelocity: int
        -OD_CANopenSlave_TargetPosition: long
    }

    %% 关键数据结构
    class STRUCT_STATE_MACHINE_INFORMATION {
        +ExecuteStep: ENUM_STEP
        +Command: ENUM_COMMAND
        +Status: ENUM_STATUS
        +StatusLock: ENUM_LOCK
    }

    class STRUCT_SYSTEM_VARIABLE_DI {
        +SwitchA: STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY
        +SwitchB: STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY
        +SwitchC: STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY
        +DI[18]: STRUCT_SYSTEM_VARIABLE_INFORMATION_ONE_IO_PROPERTY
    }

    %% 模块交互关系
    AWSMainFunction --> StateMachine : 调用状态机\n(StateMachineRun)
    AWSMainFunction --> SystemVariable : 读写系统变量\n(SystemVariableRun*MS)
    StateMachine --> MotionControl : 控制指令\n(MOTION_CONTROL_MODE_*)
    StateMachine --> FaultCode : 查询故障状态\n(FaultCodeGetStatus)
    StateMachine --> CANopenSlave : 读写对象字典\n(OD_CANopenSlave_*)
    SystemVariable --> StateMachine : 提供DI状态\n(SystemVariablDI)
    SystemVariable --> CANopenSlave : 同步状态字\n(OD_CANopenSlave_Statusword)
    MotionControl ..> AWSMainFunction : 使用传动比参数\n(AWS.Write.GearRatio)

    %% 关键变量交互说明
    note for StateMachine "状态转换依赖:\n- SystemVariablDI.SwitchA/B/C\n- OD_CANopenSlave_Controlword_Value\n- FaultCodeGetStatus()"
    note for MotionControl "运动参数:\n- OD_CANopenSlave_ProfileVelocity\n- OD_CANopenSlave_TargetPosition\n- AWS.Write.GearRatio"