/**
 * @file ServoSimCore.h
 * @brief Servo simulation core header
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#ifndef SERVO_SIM_CORE_H
#define SERVO_SIM_CORE_H

#ifdef __cplusplus
extern "C" {
#endif

#ifdef _WIN32
#define EXPORT __declspec(dllexport)
#else
#define EXPORT
#endif



/* Function prototypes - Big CamelCase naming */
EXPORT void InitSimulation(ServoSimState* pState);

EXPORT void SimulationStep(ServoSimState* pState);

EXPORT void ResetSimulation(ServoSimState* pState);

EXPORT double DegToRad(double dDeg);

EXPORT double RadToDeg(double dRad);

#ifdef __cplusplus
}
#endif

#endif /* SERVO_SIM_CORE_H */

