/**
 * @file stateMachine.c
 * @brief State machine implementation for pitch control system
 * @note Compliant with MISRA-C:2004 and C90 standard
 */

#include "Lib\BackgroundInterface\BackgroundInterface.h"
#include "externInputInterface.h"
#include "ServoSimCore.h"

unsigned char StatemachineRunToEmergencyFlag;
STRUCT_STATE_MACHINE_INFORMATION stcStateMachine;

/* Bit manipulation macros - MISRA-C compliant */
#define GET_BIT(x, bit) (((x) & (1U << (bit))) >> (bit))

/* Bit manipulation functions with range checking - Big CamelCase naming */
ui16 SetBit(ui16 uiNum, usi8 usiBitPos) {
    ui16 uiResult = uiNum;
    if (usiBitPos < 16U) {  /* Range check for ui16 */
        uiResult = uiNum | (1U << usiBitPos);
    }
    return uiResult;
}

ui16 ClrBit(ui16 uiNum, usi8 usiBitPos) {
    ui16 uiResult = uiNum;
    if (usiBitPos < 16U) {  /* Range check for ui16 */
        uiResult = uiNum & (~(1U << usiBitPos));
    }
    return uiResult;
}

ServoSimState gSTservoSimState;
stGlobalVars gstSEC;
eSystemState eCurrentState;

/* Global transition condition variables - initialized to 0 */
ui16 guiTf18Condition = 0;    /* PowerOn->Manual condition */
ui16 guiTf13Condition = 0U;    /* PowerOn->Standby condition */
ui16 guiTf36Condition = 0U;    /* Standby->PreNormal condition */
ui16 guiTf64Condition = 0U;    /* PreNormal->Normal condition */
ui16 guiTf65Condition = 0U;    /* PreNormal->VortexRun condition */
ui16 guiTf910Condition = 0U;   /* VortexRun->VortexBrake condition */
ui16 guiTf105Condition = 0U;   /* VortexBrake->Emergency condition */
ui16 guiTf45Condition = 0U;    /* Normal->Emergency condition */
ui16 guiTf57Condition = 0U;    /* Emergency->BackupTest condition */
ui16 guiTf73Condition = 0U;    /* BackupTest->Standby condition */
ui16 guiTf83Condition = 0U;    /* Manual->Standby condition */
ui16 guiTf53Condition = 0U;    /* Emergency->Standby condition */
ui16 guiTf95Condition = 0U;    /* VortexRun->Emergency condition */

/* Global state action counters - initialized to 0 */
ui32 guiPowerOnCnt = 0U;       /* Power on state counter */
ui32 guiStandbyCnt = 0U;       /* Standby state counter */
ui32 guiPreNormalCnt = 0U;     /* Pre-normal state counter */
ui32 guiNormalCnt = 0U;        /* Normal state counter */
ui32 guiEmergencyCnt = 0U;     /* Emergency state counter */
ui32 guiBackupTestCnt = 0U;    /* Backup test state counter */
ui32 guiManualCnt = 0U;        /* Manual state counter */
ui32 guiVortexRunCnt = 0U;     /* Vortex run state counter */
ui32 guiVortexBrakeCnt = 0U;   /* Vortex brake state counter */

/* Global current state with dual protection - CRITICAL VARIABLES */
eSystemState gCurrentState = ESTATE_POWER_ON;
eSystemState gCurrentStateBak = ESTATE_POWER_ON;

/* Get current state with dual protection check - CRITICAL FUNCTION */
eSystemState GetCurrentState(void) {
    eSystemState eState;

    /* C90 compliance - declare variables at beginning */
    eState = gCurrentState;

    /* Critical function - dual protection check */
    if (gCurrentState != gCurrentStateBak) {
        /* State corruption detected - reset to safe state */
        gCurrentState = ESTATE_EMERGENCY;
        gCurrentStateBak = ESTATE_EMERGENCY;
        eState = ESTATE_EMERGENCY;
    }

    return eState;
}

/* Static function prototypes - internal use only - Big CamelCase naming */
void StateMachineInit(void);
static void HandlePowerOn(void);
static void HandleStandby(void);
static void HandlePreNormal(void);
static void HandleNormal(void);
static void HandleEmergency(void);
static void HandleBackupTest(void);
static void HandleManual(void);
static void HandleVortexRun(void);
static void HandleVortexBrake(void);

/* Transition check functions - Big CamelCase naming */
static bool CheckTf18(void);   /* PowerOn->Manual */
static bool CheckTf13(void);   /* PowerOn->Standby */
static bool CheckTf35(void);   /* Standby->Emergency */
static bool CheckTf36(void);   /* Standby->PreNormal */
static bool CheckTf64(void);   /* PreNormal->Normal */
static bool CheckTf65(void);   /* PreNormal->VortexRun */
static bool CheckTf910(void);  /* VortexRun->VortexBrake */
static bool CheckTf105(void);  /* VortexBrake->Emergency */
static bool CheckTf45(void);   /* Normal->Emergency */
static bool CheckTf57(void);   /* Emergency->BackupTest */
static bool CheckTf73(void);   /* BackupTest->Standby */
static bool CheckTf83(void);   /* Manual->Standby */
static bool CheckTf53(void);   /* Emergency->Standby */
static bool CheckTf95(void);   /* VortexRun->Emergency */

void MotorRun(ENUM_MOTION_CONTROL_MODE_SELECT Mode, unsigned int CycleTime,float TargetPosition, float TargetSpeed,float MaxSpeed, float KP1, float KP2)
{
#ifdef ENABLE_RUNTIME_SWITCH
    /* Development/Test version: Use runtime switching interface */
    MotorControlInterface_Run(Mode, CycleTime, TargetPosition, TargetSpeed, MaxSpeed, KP1, KP2);
#else
    /* Production version: Direct call to real hardware control */
    MotionControlRun(Mode, CycleTime, TargetPosition, TargetSpeed, MaxSpeed, KP1, KP2);
#endif
}

void StateMachineInit(void)
{
    memset(&gstSEC,0,sizeof(gstSEC));
#ifdef ENABLE_RUNTIME_SWITCH
    /* Initialize motor control interface for development/test version */
    MotorControlInterface_Init();
#endif
    /* Production version needs no additional initialization */
}
/* State transition function with dual protection - CRITICAL FUNCTION */
static void TransitionTo(eSystemState eNewState) {
    /* Range check for valid state */
    if (eNewState < ESTATE_MAX) {
        gCurrentState = eNewState;
        gCurrentStateBak = eNewState;  /* Dual protection */
    }
}

/* Check transition conditions - max nesting depth 2 levels */
static void CheckTransitions(void) {

    /* C90 compliance - declare variables at beginning */
    eCurrentState = GetCurrentState();

    switch (eCurrentState) {
        case ESTATE_POWER_ON:
            if (CheckTf18() == TRUE) {
                TransitionTo(ESTATE_MANUAL);
            } else if (CheckTf13() == TRUE) {
                TransitionTo(ESTATE_STANDBY);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_STANDBY:
            if (CheckTf36() == TRUE) {
                TransitionTo(ESTATE_PRE_NORMAL);
            } else if (CheckTf35() == TRUE) {
                TransitionTo(ESTATE_EMERGENCY);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_PRE_NORMAL:
            if (CheckTf64() == TRUE) {
                TransitionTo(ESTATE_NORMAL);
            } else if (CheckTf65() == TRUE) {
                TransitionTo(ESTATE_VORTEX_RUN);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_NORMAL:
            if (CheckTf45() == TRUE) {
                TransitionTo(ESTATE_EMERGENCY);
            }
            break;

        case ESTATE_VORTEX_RUN:
            if (CheckTf910() == TRUE) {
                TransitionTo(ESTATE_VORTEX_BRAKE);
            } else if (CheckTf95() == TRUE) {
                TransitionTo(ESTATE_EMERGENCY);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_VORTEX_BRAKE:
            if (CheckTf105() == TRUE) {
                TransitionTo(ESTATE_EMERGENCY);
            }
            break;

        case ESTATE_EMERGENCY:
            if (CheckTf57() == TRUE) {
                TransitionTo(ESTATE_BACKUP_TEST);
            } else if (CheckTf53() == TRUE) {
                TransitionTo(ESTATE_STANDBY);
            } else {
                /* No transition - stay in current state */
            }
            break;

        case ESTATE_BACKUP_TEST:
            if (CheckTf73() == TRUE) {
                TransitionTo(ESTATE_STANDBY);
            }
            break;

        case ESTATE_MANUAL:
            if (CheckTf83() == TRUE) {
                TransitionTo(ESTATE_STANDBY);
            }
            break;

        default:
            /* Invalid state - transition to emergency */
            TransitionTo(ESTATE_EMERGENCY);
            break;
    }
}

/* Main state machine processing function - max 100 lines */
void StateMachineProcess(void) {

    /* Check and execute state transitions */
    CheckTransitions();

    /* Get current state for processing */
    eCurrentState = GetCurrentState();

    /* Execute current state processing logic */
    switch (eCurrentState) {
        case ESTATE_POWER_ON:
            HandlePowerOn();
            break;
        case ESTATE_STANDBY:
            HandleStandby();
            break;
        case ESTATE_PRE_NORMAL:
            HandlePreNormal();
            break;
        case ESTATE_NORMAL:
            HandleNormal();
            break;
        case ESTATE_EMERGENCY:
            HandleEmergency();
            break;
        case ESTATE_BACKUP_TEST:
            HandleBackupTest();
            break;
        case ESTATE_MANUAL:
            HandleManual();
            break;
        case ESTATE_VORTEX_RUN:
            HandleVortexRun();
            break;
        case ESTATE_VORTEX_BRAKE:
            HandleVortexBrake();
            break;
        default:
            /* Invalid state - force to emergency */
            TransitionTo(ESTATE_EMERGENCY);
            break;
    }
}

/* State handling function implementations */
static void HandlePowerOn(void) {

    /* Increment counter with overflow protection */
    if (guiPowerOnCnt < 0xFFFFFFFFU) {
        guiPowerOnCnt++;
    }

    if (gstSEC.gbDriverIDCheckPassed == FALSE){
        gstSEC.gbDriverIDCheckPassed = TRUE;
    }

    InitSimulation( &gSTservoSimState );
    gSTservoSimState.iRunning = 1;
 //   gstSEC.gbDriverIDCheckPassed = TRUE;
    gstSEC.gbTCUNormalModeCmd = TRUE;
    gstSEC.guiGearRatioAll = 3000U;
//    gstSEC.gbSafetyLineFb = TRUE;
    /* Initialize system hardware */
    /* Add specific power-on logic here */
}

static void HandleStandby(void) {

    /* C90 compliance - declare variables at beginning */

    /* Increment counter with overflow protection */
    if (guiStandbyCnt < 0xFFFFFFFFU) {
        guiStandbyCnt++;
    }
    /* Standby mode logic */
    /* Add specific standby logic here */

}

static void HandlePreNormal(void) {
    float fTempPosition;
    /* Increment counter with overflow protection */
    if (guiPreNormalCnt < 0xFFFFFFFFU) {
        guiPreNormalCnt++;
    }
    /* Explicit type conversion with range checking */
    gstSEC.giCommandMotorPosition = 8900;
    gstSEC.giCommandMotorSpeedLimit = 100;

    /* Explicit type conversion with range checking */
    fTempPosition = ((float)gstSEC.giCommandMotorPosition*0.01) * (float)gstSEC.guiGearRatioAll;
    if (fTempPosition >= -2147483648.0f && fTempPosition <= 2147483647.0f) {
        gSTservoSimState.dPositionCommand = (double)fTempPosition;
    }
    gSTservoSimState.dSpeedLimit = gstSEC.giCommandMotorSpeedLimit*0.01*(float)gstSEC.guiGearRatioAll;
    /* Explicit type conversion with range checking */
    if (gstSEC.guiGearRatioAll != 0U) {
        fTempPosition = (float)(gSTservoSimState.dMotorPosition*100/(double)gstSEC.guiGearRatioAll);
        if (fTempPosition >= -32768.0f && fTempPosition <= 32767.0f) {
            gstSEC.giMotorPosition = (i16)fTempPosition;
        }
    }
    gstSEC.giMotorSpeed = (float)(gSTservoSimState.dMotorSpeed*100/(double)gstSEC.guiGearRatioAll);
    if ((gstSEC.giMotorPosition > 8850)&&(gstSEC.giMotorPosition < 8950)) {
        gstSEC.gbPreNormalPosReached = TRUE;
    }
}

static void HandleNormal(void) {
    /* Increment counter with overflow protection */
    if (guiNormalCnt < 0xFFFFFFFFU) {
        guiNormalCnt++;
    }

    FaultCodeGetTriggerStatus(ENUM_FC_INVERTER_U_ITE);
    FaultCodeGetTriggerStatus(ENUM_FC_INVERTER_U_MTE);

    gstSEC.giCommandMotorPosition = 7000;
    gstSEC.giCommandMotorSpeedLimit = 500;

    MotorRun(MOTION_CONTROL_MODE_STOP,0,gstSEC.giCommandMotorPosition,gstSEC.giCommandMotorSpeedLimit,gstSEC.giCommandMotorSpeedLimit,0,0);

    if ((gstSEC.giMotorPosition > 6950)&&(gstSEC.giMotorPosition < 7050)) {
        FaultCodeSet(ENUM_FC_INVERTER_U_ITE,1,0,0);
        FaultCodeSet(ENUM_FC_INVERTER_U_MTE,1,0,0);
    }
 /*   gSTservoSimState.dPositionCommand = ((float)gstSEC.giCommandMotorPosition*0.01) * (float)gstSEC.guiGearRatioAll;
    gSTservoSimState.dSpeedLimit = gstSEC.giCommandMotorSpeedLimit*0.01*(float)gstSEC.guiGearRatioAll;
    gstSEC.giMotorPosition = (float)(gSTservoSimState.dMotorPosition*100/(double)gstSEC.guiGearRatioAll);
    gstSEC.giMotorSpeed = (float)(gSTservoSimState.dMotorSpeed*100/(double)gstSEC.guiGearRatioAll);*/

    /* Normal operation logic */
    /* Add specific normal operation logic here */
}

static void HandleEmergency(void) {
    /* Increment counter with overflow protection */
    if (guiEmergencyCnt < 0xFFFFFFFFU) {
        guiEmergencyCnt++;
    }

    gstSEC.giCommandMotorPosition = 9100;
    gstSEC.giCommandMotorSpeedLimit = 300;
    gSTservoSimState.dPositionCommand = ((float)gstSEC.giCommandMotorPosition*0.01) * (float)gstSEC.guiGearRatioAll;
    gSTservoSimState.dSpeedLimit = gstSEC.giCommandMotorSpeedLimit*0.01*(float)gstSEC.guiGearRatioAll;
    gstSEC.giMotorPosition = (float)(gSTservoSimState.dMotorPosition*100/(double)gstSEC.guiGearRatioAll);
    gstSEC.giMotorSpeed = (float)(gSTservoSimState.dMotorSpeed*100/(double)gstSEC.guiGearRatioAll);

    gstSEC.gbSafetyRelayClose = false;

    if ((gstSEC.giMotorPosition > 9095)&&(gstSEC.giMotorPosition < 9105)) {
        gSTservoSimState.iRunning = 0;
    }

    /* Emergency handling logic */
    /* Add specific emergency logic here */
}

static void HandleBackupTest(void) {
    /* Increment counter with overflow protection */
    if (guiBackupTestCnt < 0xFFFFFFFFU) {
        guiBackupTestCnt++;
    }

    /* Backup test logic */
    /* Add specific backup test logic here */
}

static void HandleManual(void) {
    /* Increment counter with overflow protection */
    if (guiManualCnt < 0xFFFFFFFFU) {
        guiManualCnt++;
    }

    /* Manual mode logic */
    /* Add specific manual mode logic here */
}

static void HandleVortexRun(void) {
    /* Increment counter with overflow protection */
    if (guiVortexRunCnt < 0xFFFFFFFFU) {
        guiVortexRunCnt++;
    }

    /* Vortex run mode logic */
    /* Add specific vortex run logic here */
}

static void HandleVortexBrake(void) {
    /* Increment counter with overflow protection */
    if (guiVortexBrakeCnt < 0xFFFFFFFFU) {
        guiVortexBrakeCnt++;
    }

    /* Vortex brake mode logic */
    /* Add specific vortex brake logic here */
}

/* Transition check function implementations */
static bool CheckTf18(void) {
    /* PowerOn->Manual transition condition */
    if ((gstSEC.gbOthersInSafePos == TRUE) && (gstSEC.gbHandleSwitchOn == FALSE) && (gstSEC.gbManualModeEnabled == TRUE)){
        return TRUE;
    } else {
        return FALSE;
    }

}

static bool CheckTf13(void) {
    /* PowerOn->Standby transition condition */
    if ((gstSEC.gbDriverIDCheckPassed == TRUE)) {
        return TRUE;
    } else {
        return FALSE;
    }
}

static bool CheckTf35(void) {
    /* Standby->Emergency transition condition */
    if ((gstSEC.gbTCUEmModeCmd == TRUE) || (gstSEC.gbDriverErrorFeatherActive == TRUE) || (gstSEC.gbSafetyLineFb == FALSE)) {
        return TRUE;
    } else {
        return FALSE;
    }
}
static bool CheckTf36(void) {
    /* Standby->PreNormal transition condition */
    if ((gstSEC.gbTCUNormalModeCmd == TRUE) && (gstSEC.gbDriverErrorFeatherActive == FALSE) && (gstSEC.gbSafetyLineFb == TRUE)) {
        return TRUE;
    } else {
        return FALSE;
    }
}

static bool CheckTf64(void) {

    /* PreNormal->Normal transition condition */
    if (gstSEC.gbPreNormalPosReached == TRUE) {
        return TRUE;
    } else {
        return FALSE;
    }

}

static bool CheckTf65(void) {
    /* PreNormal->VortexRun transition condition */
    return (GET_BIT(guiTf65Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf910(void) {
    /* VortexRun->VortexBrake transition condition */
    return (GET_BIT(guiTf910Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf105(void) {
    /* VortexBrake->Emergency transition condition */
    return (GET_BIT(guiTf105Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf45(void) {
    /* Normal->Emergency transition condition */
    if (FaultCodeGetTriggerStatus(ENUM_FC_INVERTER_U_ITE) == TRUE) {
        return TRUE;
    } else {
        return FALSE;
    }
}

static bool CheckTf57(void) {
    /* Emergency->BackupTest transition condition */
    return (GET_BIT(guiTf57Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf73(void) {
    /* BackupTest->Standby transition condition */
    return (GET_BIT(guiTf73Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf83(void) {
    /* Manual->Standby transition condition */
    return (GET_BIT(guiTf83Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf53(void) {
    /* Emergency->Standby transition condition */
    return (GET_BIT(guiTf53Condition, 0U) == 1U) ? TRUE : FALSE;
}

static bool CheckTf95(void) {
    /* VortexRun->Emergency transition condition */
    return (GET_BIT(guiTf95Condition, 0U) == 1U) ? TRUE : FALSE;
}
