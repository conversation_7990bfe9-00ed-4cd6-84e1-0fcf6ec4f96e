################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/%.obj: ../Source/Z_Application/Error/%.c $(GEN_OPTS) | $(GEN_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: C2000 Compiler'
	"D:/ti/ccsv8/tools/compiler/ti-cgt-c2000_18.1.4.LTS/bin/cl2000" -v28 -ml -mt --float_support=fpu32 --include_path="D:/CCS_Program/User_SHE_WITHOUT_PLCAugment/Include" --advice:performance=all --define=_DEBUG --define=LARGE_MODEL -g --diag_warning=225 --diag_wrap=off --display_error_number --issue_remarks --obj_directory="D:/Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build" --preproc_with_compile --preproc_dependency="Source/Z_Application/Error/$(basename $(<F)).d_raw" $(GEN_OPTS__FLAG) "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


