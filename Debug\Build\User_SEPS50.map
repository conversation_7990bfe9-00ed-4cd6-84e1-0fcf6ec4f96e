******************************************************************************
             TMS320C2000 Linker PC v18.1.4                     
******************************************************************************
>> Linked Thu May 15 11:48:18 2025

OUTPUT FILE NAME:   <D:/work/CCS/workspace_v8/DSP2833x_examples/ZONCN/Shanghai_Electric/With_PLC/User_SEPS50/../../../Shanghai_Electric/With_PLC/User_SEPS50/Debug/Build/User_SEPS50.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 003108aa


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
PAGE 0:
  RAML0                 00008000   00007f00  000079fe  00000502  RWIX
  RAML1                 0000ff00   00001000  0000001f  00000fe1  RWIX
  E_RAM_USER            00200000   00040000  00035728  0000a8d8  RWIX
  BEGIN                 00300000   00000002  00000002  00000000  RWIX
  FLASHH                00300002   0002fffd  000133fc  0001cc01  RWIX
  FLASHB                00330000   00008000  00000000  00008000  RWIX
  FLASHA                00338000   00007f80  00000000  00007f80  RWIX
  CSM_RSVD              0033ff80   00000076  00000000  00000076  RWIX
  CSM_PWL               0033fff8   00000008  00000000  00000008  RWIX
  ADC_CAL               00380080   00000009  00000007  00000002  RWIX
  OTP                   00380400   00000400  00000000  00000400  RWIX
  IQTABLES              003fe000   00000b50  00000000  00000b50  RWIX
  IQTABLES2             003feb50   0000008c  00000000  0000008c  RWIX
  FPUTABLES             003febdc   000006a0  00000000  000006a0  RWIX
  ROM                   003ff27c   00000d44  00000000  00000d44  RWIX
  RESET                 003fffc0   00000002  00000000  00000002  RWIX
  VECTORS               003fffc2   0000003e  00000000  0000003e  RWIX

PAGE 1:
  BOOT_RSVD             00000000   00000050  00000000  00000050  RWIX
  RAMM0                 00000050   000003b0  00000000  000003b0  RWIX
  RAMM1                 00000400   00000400  00000400  00000000  RWIX
  DEV_EMU               00000880   00000180  000000d0  000000b0  RWIX
  FLASH_REGS            00000a80   00000060  00000008  00000058  RWIX
  CSM                   00000ae0   00000010  00000010  00000000  RWIX
  ADC_MIRROR            00000b00   00000010  00000010  00000000  RWIX
  XINTF                 00000b20   00000020  0000001e  00000002  RWIX
  CPU_TIMER0            00000c00   00000008  00000008  00000000  RWIX
  CPU_TIMER1            00000c08   00000008  00000008  00000000  RWIX
  CPU_TIMER2            00000c10   00000008  00000008  00000000  RWIX
  PIE_CTRL              00000ce0   00000020  0000001a  00000006  RWIX
  PIE_VECT              00000d00   00000100  00000100  00000000  RWIX
  DMA                   00001000   00000200  000000e0  00000120  RWIX
  MCBSPA                00005000   00000040  00000025  0000001b  RWIX
  MCBSPB                00005040   00000040  00000025  0000001b  RWIX
  ECANA                 00006000   00000040  00000034  0000000c  RWIX
  ECANA_LAM             00006040   00000040  00000040  00000000  RWIX
  ECANA_MOTS            00006080   00000040  00000040  00000000  RWIX
  ECANA_MOTO            000060c0   00000040  00000040  00000000  RWIX
  ECANA_MBOX            00006100   00000100  00000100  00000000  RWIX
  ECANB                 00006200   00000040  00000034  0000000c  RWIX
  ECANB_LAM             00006240   00000040  00000040  00000000  RWIX
  ECANB_MOTS            00006280   00000040  00000040  00000000  RWIX
  ECANB_MOTO            000062c0   00000040  00000040  00000000  RWIX
  ECANB_MBOX            00006300   00000100  00000100  00000000  RWIX
  EPWM1                 00006800   00000022  00000022  00000000  RWIX
  EPWM2                 00006840   00000022  00000022  00000000  RWIX
  EPWM3                 00006880   00000022  00000022  00000000  RWIX
  EPWM4                 000068c0   00000022  00000022  00000000  RWIX
  EPWM5                 00006900   00000022  00000022  00000000  RWIX
  EPWM6                 00006940   00000022  00000022  00000000  RWIX
  ECAP1                 00006a00   00000020  00000020  00000000  RWIX
  ECAP2                 00006a20   00000020  00000020  00000000  RWIX
  ECAP3                 00006a40   00000020  00000020  00000000  RWIX
  ECAP4                 00006a60   00000020  00000020  00000000  RWIX
  ECAP5                 00006a80   00000020  00000020  00000000  RWIX
  ECAP6                 00006aa0   00000020  00000020  00000000  RWIX
  EQEP1                 00006b00   00000040  00000040  00000000  RWIX
  EQEP2                 00006b40   00000040  00000040  00000000  RWIX
  GPIOCTRL              00006f80   00000040  0000002e  00000012  RWIX
  GPIODAT               00006fc0   00000020  00000020  00000000  RWIX
  GPIOINT               00006fe0   00000020  0000000a  00000016  RWIX
  SYSTEM                00007010   00000020  00000020  00000000  RWIX
  SPIA                  00007040   00000010  00000010  00000000  RWIX
  SCIA                  00007050   00000010  00000010  00000000  RWIX
  XINTRUPT              00007070   00000010  00000010  00000000  RWIX
  ADC                   00007100   00000020  0000001e  00000002  RWIX
  SCIB                  00007750   00000010  00000010  00000000  RWIX
  SCIC                  00007770   00000010  00000010  00000000  RWIX
  I2CA                  00007900   00000040  00000022  0000001e  RWIX
  CSM_PWL               0033fff8   00000008  00000008  00000000  RWIX


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.cinit     0    00310c32    00001ae0     
                  00310c32    000014fa     ObjectDictionary.obj (.cinit)
                  0031212c    0000021f     User_SEPS50_Lib.lib : CANopenSlave.obj (.cinit)
                  0031234b    000000d3                         : Inverter.obj (.cinit)
                  0031241e    00000082                         : BackgroundInterface.obj (.cinit)
                  003124a0    00000073                         : Rotary.obj (.cinit)
                  00312513    0000005b                         : CANopenMaster.obj (.cinit)
                  0031256e    0000003c                         : MotionControl.obj (.cinit)
                  003125aa    00000036                         : Manual.obj (.cinit)
                  003125e0    00000033                         : Logger.obj (.cinit)
                  00312613    0000002a                         : FaultCode.obj (.cinit)
                  0031263d    00000021     StateMachine.obj (.cinit)
                  0031265e    0000001c     SystemVariable.obj (.cinit)
                  0031267a    00000014     User_SEPS50_Lib.lib : ParameterSD.obj (.cinit)
                  0031268e    00000011                         : HistoryError.obj (.cinit)
                  0031269f    00000011                         : ModBusTCP.obj (.cinit)
                  003126b0    00000010                         : File.obj (.cinit)
                  003126c0    00000010                         : SD_SPI_Initialization.obj (.cinit)
                  003126d0    0000000e     rts2800_fpu32.lib : exit.c.obj (.cinit)
                  003126de    0000000c     Modbus.obj (.cinit)
                  003126ea    00000008     AWSMainFunction.obj (.cinit)
                  003126f2    00000005     rts2800_fpu32.lib : _lock.c.obj (.cinit:__lock)
                  003126f7    00000005                       : _lock.c.obj (.cinit:__unlock)
                  003126fc    00000004     User_SEPS50_Lib.lib : IO.obj (.cinit)
                  00312700    00000004                         : SD_SPI_Transmission.obj (.cinit)
                  00312704    00000004                         : SSI.obj (.cinit)
                  00312708    00000004                         : diskio.obj (.cinit)
                  0031270c    00000004     rts2800_fpu32.lib : errno.c.obj (.cinit)
                  00312710    00000002     --HOLE-- [fill = 0]

.pinit     0    00300002    00000000     UNINITIALIZED

.ebss      0    00008000    000079fe     UNINITIALIZED
                  00008000    00001e40     User_SEPS50_Lib.lib : ParameterSD.obj (.ebss)
                  00009e40    000014c8                         : FaultCode.obj (.ebss)
                  0000b308    0000002e                         : IO.obj (.ebss)
                  0000b336    00000008                         : RTC.obj (.ebss)
                  0000b33e    00000002     AWSMainFunction.obj (.ebss)
                  0000b340    000013b8     ObjectDictionary.obj (.ebss)
                  0000c6f8    00000006     User_SEPS50_Lib.lib : SSI.obj (.ebss)
                  0000c6fe    00000002                         : SD_SPI_Registers.obj (.ebss)
                  0000c700    000007da                         : File.obj (.ebss)
                  0000ceda    00000026                         : HistoryError.obj (.ebss)
                  0000cf00    000006dc                         : Logger.obj (.ebss)
                  0000d5dc    00000018                         : DSP2833x_CpuTimers.obj (.ebss)
                  0000d5f4    00000006     rts2800_fpu32.lib : exit.c.obj (.ebss)
                  0000d5fa    00000004     User_SEPS50_Lib.lib : SD_SPI_Transmission.obj (.ebss)
                  0000d5fe    00000002     rts2800_fpu32.lib : _lock.c.obj (.ebss:__lock)
                  0000d600    00000620     User_SEPS50_Lib.lib : CANopenSlave.obj (.ebss)
                  0000dc20    00000012                         : Manual.obj (.ebss)
                  0000dc32    0000000e     StateMachine.obj (.ebss)
                  0000dc40    000005b6     User_SEPS50_Lib.lib : ModBusTCP.obj (.ebss)
                  0000e1f6    00000002     rts2800_fpu32.lib : _lock.c.obj (.ebss:__unlock)
                  0000e1f8    00000001     User_SEPS50_Lib.lib : diskio.obj (.ebss)
                  0000e1f9    00000001     rts2800_fpu32.lib : errno.c.obj (.ebss)
                  0000e1fa    00000006     --HOLE--
                  0000e200    00000568     User_SEPS50_Lib.lib : Inverter.obj (.ebss)
                  0000e768    00000018     --HOLE--
                  0000e780    00000460                         : CANopenMaster.obj (.ebss)
                  0000ebe0    00000020     --HOLE--
                  0000ec00    00000308     Modbus.obj (.ebss)
                  0000ef08    0000002c     User_SEPS50_Lib.lib : SD_SPI_Initialization.obj (.ebss)
                  0000ef34    0000000c     --HOLE--
                  0000ef40    000002b6                         : MotionControl.obj (.ebss)
                  0000f1f6    0000000a     --HOLE--
                  0000f200    00000280     Parameter.obj (.ebss)
                  0000f480    0000025a     User_SEPS50_Lib.lib : FAT.obj (.ebss)
                  0000f6da    00000026     --HOLE--
                  0000f700    000000d2                         : BackgroundInterface.obj (.ebss)
                  0000f7d2    0000002e     --HOLE--
                  0000f800    00000094     Error.obj (.ebss)
                  0000f894    0000002c     --HOLE--
                  0000f8c0    00000094     SystemVariable.obj (.ebss)
                  0000f954    0000002c     --HOLE--
                  0000f980    0000003e     User_SEPS50_Lib.lib : Rotary.obj (.ebss)
                  0000f9be    00000002     --HOLE--
                  0000f9c0    0000003e                         : disk_sd.obj (.ebss)

EXTEND_RAM 
*          0    00200000    00035728     UNINITIALIZED
                  00200000    00031b50     User_SEPS50_Lib.lib : Logger.obj (EXTEND_RAM)
                  00231b50    00000030     --HOLE--
                  00231b80    00001f40                         : HistoryError.obj (EXTEND_RAM)
                  00233ac0    00001850                         : FaultCode.obj (EXTEND_RAM)
                  00235310    00000030     --HOLE--
                  00235340    000003e8                         : Inverter.obj (EXTEND_RAM)

codestart 
*          0    00300000    00000002     
                  00300000    00000002     DSP2833x_CodeStartBranch.obj (codestart)

.text      0    00300002    00010c11     
                  00300002    000033a3     User_SEPS50_Lib.lib : CANopenMaster.obj (.text)
                  003033a5    00002017                         : FaultCode.obj (.text)
                  003053bc    00001c59                         : FAT.obj (.text)
                  00307015    00001393                         : Inverter.obj (.text)
                  003083a8    00001075                         : CANopenSlave.obj (.text)
                  0030941d    00000b2e                         : ModBusTCP.obj (.text)
                  00309f4b    000009bb                         : BackgroundInterface.obj (.text)
                  0030a906    00000965                         : ParameterSD.obj (.text)
                  0030b26b    00000946                         : File.obj (.text)
                  0030bbb1    000008d0                         : Logger.obj (.text)
                  0030c481    000006df     StateMachine.obj (.text)
                  0030cb60    00000542     User_SEPS50_Lib.lib : MotionControl.obj (.text)
                  0030d0a2    00000538                         : BackgroundInterface.obj (.text:retain)
                  0030d5da    000004d4     SystemVariable.obj (.text)
                  0030daae    000004c8     Modbus.obj (.text)
                  0030df76    000004b4     User_SEPS50_Lib.lib : IO.obj (.text)
                  0030e42a    00000440                         : Rotary.obj (.text)
                  0030e86a    0000038e                         : Manual.obj (.text)
                  0030ebf8    0000032a                         : DSP2833x_DefaultIsr.obj (.text:retain)
                  0030ef22    0000030b                         : HistoryError.obj (.text)
                  0030f22d    0000019f                         : RTC.obj (.text)
                  0030f3cc    00000185                         : SD_SPI_Initialization.obj (.text)
                  0030f551    0000015c                         : DSP2833x_ECan.obj (.text)
                  0030f6ad    00000152                         : diskio.obj (.text)
                  0030f7ff    0000014c     Error.obj (.text)
                  0030f94b    00000123     User_SEPS50_Lib.lib : DSP2833x_SysCtrl.obj (.text)
                  0030fa6e    00000119                         : DSP2833x_I2C.obj (.text)
                  0030fb87    00000107     rts2800_fpu32.lib : ll_div28.asm.obj (.text)
                  0030fc8e    000000f9     User_SEPS50_Lib.lib : SD_SPI_Write.obj (.text)
                  0030fd87    000000f6                         : DSP2833x_Xintf.obj (.text)
                  0030fe7d    000000f1     rts2800_fpu32.lib : e_asinf.c.obj (.text)
                  0030ff6e    000000d6     User_SEPS50_Lib.lib : SD_SPI_Read.obj (.text)
                  00310044    000000d4     rts2800_fpu32.lib : e_atan2f.c.obj (.text)
                  00310118    000000cd     User_SEPS50_Lib.lib : SD_SPI_Registers.obj (.text)
                  003101e5    000000ca     rts2800_fpu32.lib : s_atanf.c.obj (.text)
                  003102af    000000a7     User_SEPS50_Lib.lib : SD_SPI_Transmission.obj (.text)
                  00310356    0000009c     rts2800_fpu32.lib : fd_add28.asm.obj (.text)
                  003103f2    00000093     User_SEPS50_Lib.lib : SSI.obj (.text)
                  00310485    00000001     rts2800_fpu32.lib : startup.c.obj (.text)
                  00310486    00000092                       : e_sqrt.c.obj (.text)
                  00310518    0000008b                       : fd_div28.asm.obj (.text)
                  003105a3    00000088                       : fs_div28.asm.obj (.text)
                  0031062b    00000083                       : fd_mpy28.asm.obj (.text)
                  003106ae    0000007b     User_SEPS50_Lib.lib : DSP2833x_CpuTimers.obj (.text)
                  00310729    0000006c                         : disk_sd.obj (.text)
                  00310795    00000061                         : DSP2833x_EPwm.obj (.text)
                  003107f6    0000005c     Parameter.obj (.text)
                  00310852    00000058     User_SEPS50_Lib.lib : DSP2833x_Adc.obj (.text)
                  003108aa    00000056     rts2800_fpu32.lib : boot28.asm.obj (.text)
                  00310900    00000050     AWSMainFunction.obj (.text)
                  00310950    0000002c     User_SEPS50_Lib.lib : DSP2833x_Spi.obj (.text)
                  0031097c    0000002b                         : DSP2833x_Sci.obj (.text)
                  003109a7    0000002a     rts2800_fpu32.lib : fd_cmp28.asm.obj (.text)
                  003109d1    0000002a                       : l_div28.asm.obj (.text)
                  003109fb    00000029                       : exit.c.obj (.text)
                  00310a24    00000028     User_SEPS50_Lib.lib : DSP2833x_PieCtrl.obj (.text)
                  00310a4c    00000026     rts2800_fpu32.lib : e_sqrtf.c.obj (.text)
                  00310a72    00000024                       : cpy_tbl.c.obj (.text)
                  00310a96    00000023                       : fd_tofsfpu32.asm.obj (.text)
                  00310ab9    00000022                       : i_div28.asm.obj (.text)
                  00310adb    00000020     User_SEPS50_Lib.lib : DSP2833x_PieVect.obj (.text)
                  00310afb    0000001e     rts2800_fpu32.lib : ll_cmp28.asm.obj (.text)
                  00310b19    0000001d                       : memcpy.c.obj (.text)
                  00310b36    0000001c                       : fs_tofdfpu32.asm.obj (.text)
                  00310b52    0000001c                       : l_tofd28.asm.obj (.text)
                  00310b6e    00000019                       : args_main.c.obj (.text)
                  00310b87    00000018                       : ll_aox28.asm.obj (.text)
                  00310b9f    00000013     User_SEPS50_Lib.lib : DSP2833x_MemCopy.obj (.text)
                  00310bb2    0000000e     rts2800_fpu32.lib : fd_sub28.asm.obj (.text)
                  00310bc0    0000000c                       : memset.c.obj (.text)
                  00310bcc    0000000b                       : u_div28.asm.obj (.text)
                  00310bd7    0000000a                       : strcmp.c.obj (.text)
                  00310be1    00000009                       : _lock.c.obj (.text)
                  00310bea    00000009                       : fd_neg28.asm.obj (.text)
                  00310bf3    00000009                       : strcat.c.obj (.text)
                  00310bfc    00000008     DSP2833x_CodeStartBranch.obj (.text)
                  00310c04    00000008     rts2800_fpu32.lib : strlen.c.obj (.text)
                  00310c0c    00000005                       : strcpy.c.obj (.text)
                  00310c11    00000002                       : pre_init.c.obj (.text)

ramfuncs   0    00310c13    0000001f     RUN ADDR = 0000ff00
                  00310c13    0000001b     User_SEPS50_Lib.lib : DSP2833x_SysCtrl.obj (ramfuncs)
                  00310c2e    00000004     DSP2833x_usDelay.obj (ramfuncs)

.stack     1    00000400    00000400     UNINITIALIZED
                  00000400    00000400     --HOLE--

.econst    0    00312712    00000cd4     
                  00312712    00000313     User_SEPS50_Lib.lib : ParameterSD.obj (.econst:.string)
                  00312a25    00000001     --HOLE-- [fill = 0]
                  00312a26    0000023a                         : FaultCode.obj (.econst:.string)
                  00312c60    00000128     Error.obj (.econst:.string)
                  00312d88    0000011e     User_SEPS50_Lib.lib : Inverter.obj (.econst:.string)
                  00312ea6    00000100                         : DSP2833x_PieVect.obj (.econst:_PieVectTableInit)
                  00312fa6    00000100                         : ModBusTCP.obj (.econst:_wCRCTable$2)
                  003130a6    00000094                         : HistoryError.obj (.econst:.string)
                  0031313a    0000006a                         : Logger.obj (.econst:.string)
                  003131a4    00000044                         : FAT.obj (.econst:.string)
                  003131e8    00000042     Parameter.obj (.econst:.string)
                  0031322a    0000003e     User_SEPS50_Lib.lib : IO.obj (.econst:_$P$T0$2)
                  00313268    0000003e                         : IO.obj (.econst:_$P$T1$3)
                  003132a6    00000034                         : Inverter.obj (.econst:_$P$T1$11)
                  003132da    00000030                         : Inverter.obj (.econst:_$P$T0$10)
                  0031330a    00000022                         : IO.obj (.econst:_$P$T4$6)
                  0031332c    00000022                         : IO.obj (.econst:_$P$T5$7)
                  0031334e    00000016                         : IO.obj (.econst:_$P$T2$4)
                  00313364    00000016                         : IO.obj (.econst:_$P$T3$5)
                  0031337a    00000014     rts2800_fpu32.lib : e_asinf.c.obj (.econst)
                  0031338e    0000000c     User_SEPS50_Lib.lib : CANopenMaster.obj (.econst)
                  0031339a    0000000c     rts2800_fpu32.lib : e_atan2f.c.obj (.econst)
                  003133a6    0000000c                       : e_sqrt.c.obj (.econst)
                  003133b2    0000000b     User_SEPS50_Lib.lib : FAT.obj (.econst:_cst$5)
                  003133bd    0000000b                         : FAT.obj (.econst:_vst$4)
                  003133c8    0000000a     rts2800_fpu32.lib : s_atanf.c.obj (.econst:_aT)
                  003133d2    00000008                       : s_atanf.c.obj (.econst:_atanhi)
                  003133da    00000008                       : s_atanf.c.obj (.econst:_atanlo)
                  003133e2    00000004                       : s_atanf.c.obj (.econst)

.switch    0    003133e6    00000018     
                  003133e6    00000018     User_SEPS50_Lib.lib : FaultCode.obj (.switch:_FaultCodePropertyWRSD)

.reset     0    003fffc0    00000002     DSECT
                  003fffc0    00000002     rts2800_fpu32.lib : boot28.asm.obj (.reset)

vectors    0    003fffc2    00000000     DSECT

.adc_cal   0    00380080    00000007     NOLOAD SECTION
                  00380080    00000007     DSP2833x_ADC_cal.obj (.adc_cal)

DevEmuRegsFile 
*          1    00000880    000000d0     UNINITIALIZED
                  00000880    000000d0     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (DevEmuRegsFile)

FlashRegsFile 
*          1    00000a80    00000008     UNINITIALIZED
                  00000a80    00000008     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (FlashRegsFile)

CsmRegsFile 
*          1    00000ae0    00000010     UNINITIALIZED
                  00000ae0    00000010     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (CsmRegsFile)

AdcMirrorFile 
*          1    00000b00    00000010     UNINITIALIZED
                  00000b00    00000010     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (AdcMirrorFile)

XintfRegsFile 
*          1    00000b20    0000001e     UNINITIALIZED
                  00000b20    0000001e     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (XintfRegsFile)

CpuTimer0RegsFile 
*          1    00000c00    00000008     UNINITIALIZED
                  00000c00    00000008     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (CpuTimer0RegsFile)

CpuTimer1RegsFile 
*          1    00000c08    00000008     UNINITIALIZED
                  00000c08    00000008     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (CpuTimer1RegsFile)

CpuTimer2RegsFile 
*          1    00000c10    00000008     UNINITIALIZED
                  00000c10    00000008     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (CpuTimer2RegsFile)

PieCtrlRegsFile 
*          1    00000ce0    0000001a     UNINITIALIZED
                  00000ce0    0000001a     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (PieCtrlRegsFile)

PieVectTableFile 
*          1    00000d00    00000100     UNINITIALIZED
                  00000d00    00000100     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (PieVectTableFile)

DmaRegsFile 
*          1    00001000    000000e0     UNINITIALIZED
                  00001000    000000e0     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (DmaRegsFile)

McbspaRegsFile 
*          1    00005000    00000025     UNINITIALIZED
                  00005000    00000025     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (McbspaRegsFile)

McbspbRegsFile 
*          1    00005040    00000025     UNINITIALIZED
                  00005040    00000025     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (McbspbRegsFile)

ECanaRegsFile 
*          1    00006000    00000034     UNINITIALIZED
                  00006000    00000034     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanaRegsFile)

ECanaLAMRegsFile 
*          1    00006040    00000040     UNINITIALIZED
                  00006040    00000040     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanaLAMRegsFile)

ECanaMOTSRegsFile 
*          1    00006080    00000040     UNINITIALIZED
                  00006080    00000040     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanaMOTSRegsFile)

ECanaMOTORegsFile 
*          1    000060c0    00000040     UNINITIALIZED
                  000060c0    00000040     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanaMOTORegsFile)

ECanaMboxesFile 
*          1    00006100    00000100     UNINITIALIZED
                  00006100    00000100     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanaMboxesFile)

ECanbRegsFile 
*          1    00006200    00000034     UNINITIALIZED
                  00006200    00000034     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanbRegsFile)

ECanbLAMRegsFile 
*          1    00006240    00000040     UNINITIALIZED
                  00006240    00000040     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanbLAMRegsFile)

ECanbMOTSRegsFile 
*          1    00006280    00000040     UNINITIALIZED
                  00006280    00000040     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanbMOTSRegsFile)

ECanbMOTORegsFile 
*          1    000062c0    00000040     UNINITIALIZED
                  000062c0    00000040     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanbMOTORegsFile)

ECanbMboxesFile 
*          1    00006300    00000100     UNINITIALIZED
                  00006300    00000100     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECanbMboxesFile)

EPwm1RegsFile 
*          1    00006800    00000022     UNINITIALIZED
                  00006800    00000022     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (EPwm1RegsFile)

EPwm2RegsFile 
*          1    00006840    00000022     UNINITIALIZED
                  00006840    00000022     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (EPwm2RegsFile)

EPwm3RegsFile 
*          1    00006880    00000022     UNINITIALIZED
                  00006880    00000022     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (EPwm3RegsFile)

EPwm4RegsFile 
*          1    000068c0    00000022     UNINITIALIZED
                  000068c0    00000022     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (EPwm4RegsFile)

EPwm5RegsFile 
*          1    00006900    00000022     UNINITIALIZED
                  00006900    00000022     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (EPwm5RegsFile)

EPwm6RegsFile 
*          1    00006940    00000022     UNINITIALIZED
                  00006940    00000022     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (EPwm6RegsFile)

ECap1RegsFile 
*          1    00006a00    00000020     UNINITIALIZED
                  00006a00    00000020     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECap1RegsFile)

ECap2RegsFile 
*          1    00006a20    00000020     UNINITIALIZED
                  00006a20    00000020     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECap2RegsFile)

ECap3RegsFile 
*          1    00006a40    00000020     UNINITIALIZED
                  00006a40    00000020     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECap3RegsFile)

ECap4RegsFile 
*          1    00006a60    00000020     UNINITIALIZED
                  00006a60    00000020     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECap4RegsFile)

ECap5RegsFile 
*          1    00006a80    00000020     UNINITIALIZED
                  00006a80    00000020     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECap5RegsFile)

ECap6RegsFile 
*          1    00006aa0    00000020     UNINITIALIZED
                  00006aa0    00000020     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ECap6RegsFile)

EQep1RegsFile 
*          1    00006b00    00000040     UNINITIALIZED
                  00006b00    00000040     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (EQep1RegsFile)

EQep2RegsFile 
*          1    00006b40    00000040     UNINITIALIZED
                  00006b40    00000040     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (EQep2RegsFile)

GpioCtrlRegsFile 
*          1    00006f80    0000002e     UNINITIALIZED
                  00006f80    0000002e     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (GpioCtrlRegsFile)

GpioDataRegsFile 
*          1    00006fc0    00000020     UNINITIALIZED
                  00006fc0    00000020     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (GpioDataRegsFile)

GpioIntRegsFile 
*          1    00006fe0    0000000a     UNINITIALIZED
                  00006fe0    0000000a     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (GpioIntRegsFile)

SysCtrlRegsFile 
*          1    00007010    00000020     UNINITIALIZED
                  00007010    00000020     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (SysCtrlRegsFile)

SpiaRegsFile 
*          1    00007040    00000010     UNINITIALIZED
                  00007040    00000010     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (SpiaRegsFile)

SciaRegsFile 
*          1    00007050    00000010     UNINITIALIZED
                  00007050    00000010     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (SciaRegsFile)

XIntruptRegsFile 
*          1    00007070    00000010     UNINITIALIZED
                  00007070    00000010     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (XIntruptRegsFile)

AdcRegsFile 
*          1    00007100    0000001e     UNINITIALIZED
                  00007100    0000001e     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (AdcRegsFile)

ScibRegsFile 
*          1    00007750    00000010     UNINITIALIZED
                  00007750    00000010     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ScibRegsFile)

ScicRegsFile 
*          1    00007770    00000010     UNINITIALIZED
                  00007770    00000010     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (ScicRegsFile)

I2caRegsFile 
*          1    00007900    00000022     UNINITIALIZED
                  00007900    00000022     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (I2caRegsFile)

CsmPwlFile 
*          1    0033fff8    00000008     UNINITIALIZED
                  0033fff8    00000008     User_SEPS50_Lib.lib : DSP2833x_GlobalVariableDefs.obj (CsmPwlFile)

MODULE SUMMARY

       Module                            code    initialized data   uninitialized data
       ------                            ----    ----------------   ------------------
    .\Build\
       ObjectDictionary.obj              0       5370               5048              
       Modbus.obj                        1224    12                 776               
       StateMachine.obj                  1759    33                 14                
       SystemVariable.obj                1236    28                 148               
       Parameter.obj                     92      66                 640               
       Error.obj                         332     296                148               
       AWSMainFunction.obj               80      8                  2                 
       DSP2833x_CodeStartBranch.obj      10      0                  0                 
       DSP2833x_usDelay.obj              8       0                  0                 
       DSP2833x_ADC_cal.obj              7       0                  0                 
    +--+---------------------------------+-------+------------------+--------------------+
       Total:                            4748    5813               6776              
                                                                                      
    ../User_SEPS50_Lib.lib
       Logger.obj                        2256    157                205356            
       FaultCode.obj                     8215    636                11544             
       CANopenMaster.obj                 13219   103                1120              
       ParameterSD.obj                   2405    807                7744              
       HistoryError.obj                  779     165                8038              
       Inverter.obj                      5011    597                2384              
       FAT.obj                           7257    90                 602               
       CANopenSlave.obj                  4213    543                1568              
       ModBusTCP.obj                     2862    273                1462              
       File.obj                          2374    16                 2010              
       BackgroundInterface.obj           3827    130                210               
       DSP2833x_GlobalVariableDefs.obj   0       0                  2678              
       MotionControl.obj                 1346    60                 694               
       IO.obj                            1204    240                46                
       Rotary.obj                        1088    115                62                
       Manual.obj                        910     54                 18                
       DSP2833x_DefaultIsr.obj           810     0                  0                 
       SD_SPI_Initialization.obj         389     16                 44                
       RTC.obj                           415     0                  8                 
       DSP2833x_ECan.obj                 348     0                  0                 
       DSP2833x_SysCtrl.obj              345     0                  0                 
       diskio.obj                        338     4                  1                 
       DSP2833x_PieVect.obj              32      256                0                 
       DSP2833x_I2C.obj                  281     0                  0                 
       SD_SPI_Write.obj                  249     0                  0                 
       DSP2833x_Xintf.obj                246     0                  0                 
       SD_SPI_Read.obj                   214     0                  0                 
       SD_SPI_Registers.obj              205     0                  2                 
       SD_SPI_Transmission.obj           167     4                  4                 
       disk_sd.obj                       108     0                  62                
       SSI.obj                           147     4                  6                 
       DSP2833x_CpuTimers.obj            123     0                  24                
       DSP2833x_EPwm.obj                 97      0                  0                 
       DSP2833x_Adc.obj                  88      0                  0                 
       DSP2833x_Spi.obj                  44      0                  0                 
       DSP2833x_Sci.obj                  43      0                  0                 
       DSP2833x_PieCtrl.obj              40      0                  0                 
       DSP2833x_MemCopy.obj              19      0                  0                 
    +--+---------------------------------+-------+------------------+--------------------+
       Total:                            61714   4270               245687            
                                                                                      
    C:\ti\ccsv8\tools\compiler\ti-cgt-c2000_18.1.4.LTS\lib\rts2800_fpu32.lib
       ll_div28.asm.obj                  263     0                  0                 
       e_asinf.c.obj                     241     20                 0                 
       s_atanf.c.obj                     202     30                 0                 
       e_atan2f.c.obj                    212     12                 0                 
       e_sqrt.c.obj                      146     12                 0                 
       fd_add28.asm.obj                  156     0                  0                 
       fd_div28.asm.obj                  139     0                  0                 
       fs_div28.asm.obj                  136     0                  0                 
       fd_mpy28.asm.obj                  131     0                  0                 
       boot28.asm.obj                    86      0                  0                 
       exit.c.obj                        41      14                 6                 
       fd_cmp28.asm.obj                  42      0                  0                 
       l_div28.asm.obj                   42      0                  0                 
       e_sqrtf.c.obj                     38      0                  0                 
       cpy_tbl.c.obj                     36      0                  0                 
       fd_tofsfpu32.asm.obj              35      0                  0                 
       i_div28.asm.obj                   34      0                  0                 
       ll_cmp28.asm.obj                  30      0                  0                 
       memcpy.c.obj                      29      0                  0                 
       fs_tofdfpu32.asm.obj              28      0                  0                 
       l_tofd28.asm.obj                  28      0                  0                 
       args_main.c.obj                   25      0                  0                 
       ll_aox28.asm.obj                  24      0                  0                 
       _lock.c.obj                       9       10                 4                 
       fd_sub28.asm.obj                  14      0                  0                 
       memset.c.obj                      12      0                  0                 
       u_div28.asm.obj                   11      0                  0                 
       strcmp.c.obj                      10      0                  0                 
       fd_neg28.asm.obj                  9       0                  0                 
       strcat.c.obj                      9       0                  0                 
       strlen.c.obj                      8       0                  0                 
       errno.c.obj                       0       4                  1                 
       strcpy.c.obj                      5       0                  0                 
       pre_init.c.obj                    2       0                  0                 
       startup.c.obj                     1       0                  0                 
    +--+---------------------------------+-------+------------------+--------------------+
       Total:                            2234    102                11                
                                                                                      
       Stack:                            0       0                  1024              
    +--+---------------------------------+-------+------------------+--------------------+
       Grand Total:                      68696   10185              253498            


GLOBAL DATA SYMBOLS: SORTED BY DATA PAGE

address     data page           name
--------    ----------------    ----
00000400      10 (00000400)     __stack

00000880      22 (00000880)     _DevEmuRegs

00000a80      2a (00000a80)     _FlashRegs

00000ae0      2b (00000ac0)     _CsmRegs

00000b00      2c (00000b00)     _AdcMirror
00000b20      2c (00000b00)     _XintfRegs

00000c00      30 (00000c00)     _CpuTimer0Regs
00000c08      30 (00000c00)     _CpuTimer1Regs
00000c10      30 (00000c00)     _CpuTimer2Regs

00000ce0      33 (00000cc0)     _PieCtrlRegs

00000d00      34 (00000d00)     _PieVectTable

00001000      40 (00001000)     _DmaRegs

00005000     140 (00005000)     _McbspaRegs

00005040     141 (00005040)     _McbspbRegs

00006000     180 (00006000)     _ECanaRegs

00006040     181 (00006040)     _ECanaLAMRegs

00006080     182 (00006080)     _ECanaMOTSRegs

000060c0     183 (000060c0)     _ECanaMOTORegs

00006100     184 (00006100)     _ECanaMboxes

00006200     188 (00006200)     _ECanbRegs

00006240     189 (00006240)     _ECanbLAMRegs

00006280     18a (00006280)     _ECanbMOTSRegs

000062c0     18b (000062c0)     _ECanbMOTORegs

00006300     18c (00006300)     _ECanbMboxes

00006800     1a0 (00006800)     _EPwm1Regs

00006840     1a1 (00006840)     _EPwm2Regs

00006880     1a2 (00006880)     _EPwm3Regs

000068c0     1a3 (000068c0)     _EPwm4Regs

00006900     1a4 (00006900)     _EPwm5Regs

00006940     1a5 (00006940)     _EPwm6Regs

00006a00     1a8 (00006a00)     _ECap1Regs
00006a20     1a8 (00006a00)     _ECap2Regs

00006a40     1a9 (00006a40)     _ECap3Regs
00006a60     1a9 (00006a40)     _ECap4Regs

00006a80     1aa (00006a80)     _ECap5Regs
00006aa0     1aa (00006a80)     _ECap6Regs

00006b00     1ac (00006b00)     _EQep1Regs

00006b40     1ad (00006b40)     _EQep2Regs

00006f80     1be (00006f80)     _GpioCtrlRegs

00006fc0     1bf (00006fc0)     _GpioDataRegs
00006fe0     1bf (00006fc0)     _GpioIntRegs

00007010     1c0 (00007000)     _SysCtrlRegs

00007040     1c1 (00007040)     _SpiaRegs
00007050     1c1 (00007040)     _SciaRegs
00007070     1c1 (00007040)     _XIntruptRegs

00007100     1c4 (00007100)     _AdcRegs

00007750     1dd (00007740)     _ScibRegs
00007770     1dd (00007740)     _ScicRegs

00007900     1e4 (00007900)     _I2caRegs

00008000     200 (00008000)     _PIProximitySwitch0CalibrationStatus
00008001     200 (00008000)     _PIProximitySwitch1CalibrationStatus
00008002     200 (00008000)     _PIMotorParameterVersion
00008003     200 (00008000)     _PICANOpenSlaveBaudrateOption
00008004     200 (00008000)     _PIUltracapacitorCalculateTestCount
00008006     200 (00008000)     _PINextDataLoggerNubmer
00008007     200 (00008000)     _InternalParametercount
00008008     200 (00008000)     _ExternalParametercount
00008009     200 (00008000)     _ParameterWRSDRun
0000800a     200 (00008000)     _ParameterConfigFinishFlag
0000800b     200 (00008000)     _PIEncoderCalibrationStatus
0000800c     200 (00008000)     _PIProximitySwitch2CalibrationStatus
0000800d     200 (00008000)     _ParameterSDHandleFlag
0000800e     200 (00008000)     _PIProximitySwitch1CalibrationForwardTriggerAngle
00008010     200 (00008000)     _PIProximitySwitch0CalibrationBackwardTriggerAngle
00008012     200 (00008000)     _PIProximitySwitch2CalibrationForwardTriggerAngle
00008014     200 (00008000)     _PIEncoderCalibrationReferenceValue
00008016     200 (00008000)     _PIProximitySwitch1CalibrationBackwardTriggerAngle
00008018     200 (00008000)     _PIProximitySwitch0CalibrationForwardTriggerAngle
0000801a     200 (00008000)     _PIUltracapacitorCalculateValue
0000801c     200 (00008000)     _PIProximitySwitch2CalibrationBackwardTriggerAngle
0000801e     200 (00008000)     _PISSIEncoderCalibrationReferenceValue
00008020     200 (00008000)     _PIUltracapacitorCalculateTestTimeLast

00008180     206 (00008180)     _InternalParameterWRCommand

00008200     208 (00008200)     _ParameterInternalWRSetInit

00008680     21a (00008680)     _ParameterExternalWRSetInit

00009e42     279 (00009e40)     _FaultCodeWRSDRun
00009e44     279 (00009e40)     _FaultCodeSystemStatus
00009e45     279 (00009e40)     _FaultCodeSDHandleFlag
00009e4b     279 (00009e40)     _FaultCodeSafetyChainStatus

00009e80     27a (00009e80)     _FaultCodeSpecialErrorJudgementCondition

00009f00     27c (00009f00)     _FaultCodePropertyWRCommand

0000a0c0     283 (0000a0c0)     _FaultCodeHandleDelay

0000a400     290 (0000a400)     _FaultCodeRealTime

0000a800     2a0 (0000a800)     _FaultCodeSortRealTime

0000ac00     2b0 (0000ac00)     _FaultCodeProperty

0000b30a     2cc (0000b300)     _IOControlICInformation
0000b336     2cc (0000b300)     _RtcReadValue
0000b337     2cc (0000b300)     _RTCDataTime

0000b340     2cd (0000b340)     _OD_CANopenSlave_ChargCtrlReg
0000b341     2cd (0000b340)     _OD_CANopenSlave_ChargISet
0000b342     2cd (0000b340)     _OD_CANopenSlave_IOControl
0000b343     2cd (0000b340)     _OD_CANopenSlave_ChargCtrlMod
0000b344     2cd (0000b340)     _OD_CANopenSlave_VoltageOfUser
0000b345     2cd (0000b340)     _OD_CANopenSlave_ChargVol
0000b346     2cd (0000b340)     _OD_CANopenSlave_ChargVSet
0000b347     2cd (0000b340)     _OD_CANopenSlave_UserPara6
0000b348     2cd (0000b340)     _OD_CANopenSlave_UserPara5
0000b349     2cd (0000b340)     _OD_CANopenSlave_UserPara8
0000b34a     2cd (0000b340)     _OD_CANopenSlave_UserPara7
0000b34b     2cd (0000b340)     _OD_CANopenSlave_DIErrorOutSideChoose
0000b34c     2cd (0000b340)     _OD_CANopenSlave_ChargIFeedback
0000b34d     2cd (0000b340)     _OD_CANopenSlave_UserPara4
0000b34e     2cd (0000b340)     _OD_CANopenSlave_PortOutData
0000b34f     2cd (0000b340)     _OD_CANopenSlave_SecPos
0000b350     2cd (0000b340)     _OD_CANopenSlave_DischargeTime
0000b351     2cd (0000b340)     _OD_CANopenSlave_SafeDIDly
0000b352     2cd (0000b340)     _OD_CANopenSlave_MotorPosNum
0000b353     2cd (0000b340)     _OD_CANopenSlave_SecCoderDir
0000b354     2cd (0000b340)     _OD_CANopenSlave_SecPosNum
0000b355     2cd (0000b340)     _OD_CANopenSlave_ChargSysStCode
0000b356     2cd (0000b340)     _OD_CANopenSlave_ChargDefectCode
0000b357     2cd (0000b340)     _OD_CANopenSlave_FltPraOfCurrent
0000b358     2cd (0000b340)     _OD_CANopenSlave_ChargIgbtTemp
0000b359     2cd (0000b340)     _OD_CANopenSlave_DischargeAccTime
0000b35a     2cd (0000b340)     _OD_CANopenSlave_MotorPos
0000b35b     2cd (0000b340)     _OD_CANopenSlave_DischargeTimeThreshold
0000b35c     2cd (0000b340)     _OD_CANopenSlave_DischargeTimeOutSlope
0000b35d     2cd (0000b340)     _OD_CANopenSlave_MaxMotorSpeed
0000b35e     2cd (0000b340)     _OD_CANopenSlave_DCLinkCircuitVoltage
0000b35f     2cd (0000b340)     _OD_CANopenSlave_ProfileAcceleration
0000b360     2cd (0000b340)     _OD_CANopenSlave_ProfileVelocity
0000b361     2cd (0000b340)     _OD_CANopenSlave_ModesOfOperationDisplay
0000b362     2cd (0000b340)     _OD_CANopenSlave_ModesOfOperation
0000b363     2cd (0000b340)     _OD_CANopenSlave_CurrentActualValue
0000b364     2cd (0000b340)     _OD_CANopenSlave_HomingSpeeds
0000b365     2cd (0000b340)     _OD_CANopenSlave_AccelerationDimensionIndex
0000b366     2cd (0000b340)     _CANopenSlave_ODNoOfElements
0000b367     2cd (0000b340)     _OD_CANopenSlave_MotorType
0000b368     2cd (0000b340)     _OD_CANopenSlave_ProfileDeceleration
0000b369     2cd (0000b340)     _OD_CANopenSlave_VelocityActualValue
0000b36a     2cd (0000b340)     _OD_CANopenSlave_AccelerationNotationIndex
0000b36b     2cd (0000b340)     _OD_CANopenSlave_FltPraOfVelocity
0000b36c     2cd (0000b340)     _OD_CANopenSlave_FltPraOfDCVol
0000b36d     2cd (0000b340)     _OD_CANopenSlave_SnOff
0000b36e     2cd (0000b340)     _OD_CANopenSlave_SnOn
0000b36f     2cd (0000b340)     _OD_CANopenSlave_UserPara10
0000b370     2cd (0000b340)     _OD_CANopenSlave_UserPara9
0000b371     2cd (0000b340)     _OD_CANopenSlave_SafeCloseDecPD
0000b372     2cd (0000b340)     _OD_CANopenSlave_VlRampFunctionTime
0000b373     2cd (0000b340)     _OD_CANopenSlave_Statusword
0000b374     2cd (0000b340)     _OD_CANopenSlave_TorqueProfileType
0000b375     2cd (0000b340)     _OD_CANopenSlave_VlSlowDownTime
0000b376     2cd (0000b340)     _OD_CANopenSlave_SavePara
0000b377     2cd (0000b340)     _OD_CANopenSlave_SafeCloseACCPD
0000b378     2cd (0000b340)     _OD_CANopenSlave_Controlword
0000b379     2cd (0000b340)     _OD_CANopenSlave_SpeedOverproofT
0000b37a     2cd (0000b340)     _OD_CANopenSlave_VPi
0000b37b     2cd (0000b340)     _OD_CANopenSlave_Hub_HumiOrTemp
0000b37c     2cd (0000b340)     _OD_CANopenSlave_OverITotal
0000b37d     2cd (0000b340)     _OD_CANopenSlave_CapCabTemp
0000b37e     2cd (0000b340)     _OD_CANopenSlave_MaxT
0000b37f     2cd (0000b340)     _OD_CANopenSlave_MotoSafeTemp

0000b380     2ce (0000b380)     _OD_CANopenSlave_MaxI
0000b381     2ce (0000b380)     _OD_CANopenSlave_RatedCurrent
0000b382     2ce (0000b380)     _OD_CANopenSlave_PPV
0000b383     2ce (0000b380)     _OD_CANopenSlave_TSet
0000b384     2ce (0000b380)     _OD_CANopenSlave_UserCVol
0000b385     2ce (0000b380)     _OD_CANopenSlave_GuardTime
0000b386     2ce (0000b380)     _OD_CANopenSlave_LifeTimeFactor
0000b387     2ce (0000b380)     _OD_CANopenSlave_ErrorRegister
0000b388     2ce (0000b380)     _OD_CANopenSlave_MotoTemp
0000b389     2ce (0000b380)     _OD_CANopenSlave_AxleCabTemp
0000b38a     2ce (0000b380)     _OD_CANopenSlave_ServiceTimeDelay
0000b38b     2ce (0000b380)     _OD_CANopenSlave_IgbtTemp
0000b38c     2ce (0000b380)     _OD_CANopenSlave_MotoHumidEn
0000b38d     2ce (0000b380)     _OD_CANopenSlave_LimitT
0000b38e     2ce (0000b380)     _OD_CANopenSlave_LimitV
0000b38f     2ce (0000b380)     _OD_CANopenSlave_InhibitTimeEmergency
0000b390     2ce (0000b380)     _OD_CANopenSlave_ProducerHeartbeatTime
0000b391     2ce (0000b380)     _OD_CANopenSlave_VDirMod
0000b392     2ce (0000b380)     _OD_CANopenSlave_SafeCloseDownTime
0000b393     2ce (0000b380)     _OD_CANopenSlave_EscRemote
0000b394     2ce (0000b380)     _OD_CANopenSlave_BKOffDelay
0000b395     2ce (0000b380)     _OD_CANopenSlave_SafeCloseUpTime
0000b396     2ce (0000b380)     _OD_CANopenSlave_BKOnDelay
0000b397     2ce (0000b380)     _OD_CANopenSlave_ModeCtrl
0000b398     2ce (0000b380)     _OD_CANopenSlave_WorkMod
0000b399     2ce (0000b380)     _OD_CANopenSlave_ErrRst
0000b39a     2ce (0000b380)     _OD_CANopenSlave_MotorPosRst
0000b39b     2ce (0000b380)     _OD_CANopenSlave_SDownTime
0000b39c     2ce (0000b380)     _OD_CANopenSlave_SUpTime
0000b39d     2ce (0000b380)     _OD_CANopenSlave_IgbtSafeTemp
0000b39e     2ce (0000b380)     _OD_CANopenSlave_BKSwitch
0000b3a0     2ce (0000b380)     _OD_CANopenSlave_TargetPosition
0000b3a2     2ce (0000b380)     _OD_CANopenSlave_CommunicationCyclePeriod
0000b3a4     2ce (0000b380)     _OD_CANopenSlave_SyncCOBID
0000b3a6     2ce (0000b380)     _OD_CANopenSlave_SynchronousWindowLength
0000b3a8     2ce (0000b380)     _OD_CANopenSlave_ManufacturerStatusRegister
0000b3aa     2ce (0000b380)     _OD_CANopenSlave_EmergencyCOBID
0000b3ac     2ce (0000b380)     _OD_CANopenSlave_PositionActualValue
0000b3ae     2ce (0000b380)     _OD_CANopenSlave_ErrorCode1
0000b3b0     2ce (0000b380)     _OD_CANopenSlave_VelocitySensorActualValue
0000b3b2     2ce (0000b380)     _OD_CANopenSlave_VelocityDemandValue
0000b3b4     2ce (0000b380)     _OD_CANopenSlave_DigitalInputs
0000b3b6     2ce (0000b380)     _OD_CANopenSlave_MotorAngelNew
0000b3b8     2ce (0000b380)     _OD_CANopenSlave_ErrorCode2
0000b3ba     2ce (0000b380)     _OD_CANopenSlave_EncAUserActPos
0000b3bc     2ce (0000b380)     _OD_CANopenSlave_DeviceType
0000b3be     2ce (0000b380)     _OD_CANopenSlave_UserPara1

0000b3c0     2cf (0000b3c0)     _OD_CANopenSlave_SecCoderAngle
0000b3c2     2cf (0000b3c0)     _OD_CANopenSlave_UserPara3
0000b3c4     2cf (0000b3c0)     _OD_CANopenSlave_UserPara2
0000b3c6     2cf (0000b3c0)     _OD_CANopenSlave_EncBUserActPos
0000b3c8     2cf (0000b3c0)     _OD_CANopenSlave_RWParaComm
0000b3ca     2cf (0000b3c0)     _OD_CANopenSlave_RWParaData
0000b3cc     2cf (0000b3c0)     _OD_CANopenSlave_TargetVelocity
0000b3ce     2cf (0000b3c0)     _OD_CANopenSlave_GearRatioMotorRevolutions
0000b3d1     2cf (0000b3c0)     _OD_CANopenSlave_ErrorBehavior
0000b3d4     2cf (0000b3c0)     _OD_CANopenSlave_DigitalOutputs
0000b3d7     2cf (0000b3c0)     _OD_CANopenSlave_PositionEncoderResolutionEncoderIncrements
0000b3da     2cf (0000b3c0)     _OD_CANopenSlave_ConsumerHeartBeatTime
0000b3de     2cf (0000b3c0)     _OD_CANopenSlave_PositionControlParameterSetManufacturer
0000b3e4     2cf (0000b3c0)     _OD_CANopenSlave_SDOParameter
0000b3ea     2cf (0000b3c0)     _OD_CANopenSlave_VelocityControlParameter
0000b3f0     2cf (0000b3c0)     _OD_CANopenSlave_ChargerErrLowVol
0000b3f8     2cf (0000b3c0)     _OD_CANopenSlave_ServoErrDYErr

0000b400     2d0 (0000b400)     _OD_CANopenSlave_ServoErrHardOverVol
0000b408     2d0 (0000b400)     _OD_CANopenSlave_ChargerErrShortCircuit
0000b410     2d0 (0000b400)     _OD_CANopenSlave_ServoErrSoftOverCurrent
0000b418     2d0 (0000b400)     _OD_CANopenSlave_ChargerErrIGBTOverTemper
0000b420     2d0 (0000b400)     _OD_CANopenSlave_ServoErrBrokenCircuit
0000b428     2d0 (0000b400)     _OD_CANopenSlave_ServoErrNULL9
0000b430     2d0 (0000b400)     _OD_CANopenSlave_ServoErrNULL8
0000b438     2d0 (0000b400)     _OD_CANopenSlave_ServoErrNULL11

0000b440     2d1 (0000b440)     _OD_CANopenSlave_ServoErrHardOverCurrent1
0000b448     2d1 (0000b440)     _OD_CANopenSlave_ServoErrHardOverCurrent
0000b450     2d1 (0000b440)     _OD_CANopenSlave_ServoErrNULL10
0000b458     2d1 (0000b440)     _OD_CANopenSlave_ServoErrLimit96
0000b460     2d1 (0000b440)     _OD_CANopenSlave_ServoErr380VErr
0000b468     2d1 (0000b440)     _OD_CANopenSlave_ServoErrShortCircuit
0000b470     2d1 (0000b440)     _OD_CANopenSlave_ServoErrCurrentOver
0000b478     2d1 (0000b440)     _OD_CANopenSlave_ServoErrBK24VLost

0000b480     2d2 (0000b480)     _OD_CANopenSlave_ServoErrIner24VLost
0000b488     2d2 (0000b480)     _OD_CANopenSlave_ServoErrVelocityOver
0000b490     2d2 (0000b480)     _OD_CANopenSlave_ServoErrDischargeFail
0000b498     2d2 (0000b480)     _OD_CANopenSlave_ServoErrOverCurrent
0000b4a0     2d2 (0000b480)     _OD_CANopenSlave_ServoErrOuter24VLost
0000b4a8     2d2 (0000b480)     _OD_CANopenSlave_ServoErrPosLost
0000b4b0     2d2 (0000b480)     _OD_CANopenSlave_ServoErrOverVol
0000b4b8     2d2 (0000b480)     _OD_CANopenSlave_ServoErrLowVol

0000b4c0     2d3 (0000b4c0)     _OD_CANopenSlave_ServoErrSSILineOff
0000b4c8     2d3 (0000b4c0)     _OD_CANopenSlave_ServoErrMotoOverTemper
0000b4d0     2d3 (0000b4c0)     _OD_CANopenSlave_ServoErrCANOpenLineOff
0000b4d8     2d3 (0000b4c0)     _OD_CANopenSlave_ServoErrIGBTOverTemper
0000b4e0     2d3 (0000b4c0)     _OD_CANopenSlave_ServoErrOverLoad
0000b4e8     2d3 (0000b4c0)     _OD_CANopenSlave_ServoErrNULL5
0000b4f0     2d3 (0000b4c0)     _OD_CANopenSlave_ServoErrNULL6
0000b4f8     2d3 (0000b4c0)     _OD_CANopenSlave_ServoErrNULL3

0000b500     2d4 (0000b500)     _OD_CANopenSlave_ServoErrNULL4
0000b508     2d4 (0000b500)     _OD_CANopenSlave_ServoErrSoftOverVol
0000b510     2d4 (0000b500)     _OD_CANopenSlave_ChargerErrOverVol
0000b518     2d4 (0000b500)     _OD_CANopenSlave_ServoErrNULL7
0000b520     2d4 (0000b500)     _OD_CANopenSlave_ServoErrTotal
0000b528     2d4 (0000b500)     _OD_CANopenSlave_ServoErrNULL2
0000b530     2d4 (0000b500)     _OD_CANopenSlave_ServoErrNULL1
0000b538     2d4 (0000b500)     _OD_CANopenSlave_ServoErrPaddleOver

0000b540     2d5 (0000b540)     _OD_CANopenSlave_ServoErrIGBTLineOff
0000b548     2d5 (0000b540)     _OD_CANopenSlave_ServoErrSaferErr
0000b550     2d5 (0000b540)     _OD_CANopenSlave_ServoErrPositionOver
0000b558     2d5 (0000b540)     _OD_CANopenSlave_ServoErrDITrigErr
0000b560     2d5 (0000b540)     _OD_CANopenSlave_ServoErrChargerComErr
0000b568     2d5 (0000b540)     _OD_CANopenSlave_ServoErrMotoLineOff
0000b570     2d5 (0000b540)     _OD_CANopenSlave_StoreParameters

0000b580     2d6 (0000b580)     _OD_CANopenSlave_IdentifyObject
0000b58a     2d6 (0000b580)     _OD_CANopenSlave_PredefineErrorField
0000b59c     2d6 (0000b580)     _OD_CANopenSlave_ManufacturerSoftwareVersion

0000b5c0     2d7 (0000b5c0)     _OD_CANopenSlave_ManufacturerDeviceName
0000b5d4     2d7 (0000b5c0)     _OD_CANopenSlave_ManufacturerHardwareVersion

0000b600     2d8 (0000b600)     _OD_CANopenSlave_RXPDOParameter

0000b640     2d9 (0000b640)     _OD_CANopenSlave_TXPDOParameter

0000b680     2da (0000b680)     _OD_CANopenSlave_RXPDOMapping

0000b700     2dc (0000b700)     _OD_CANopenSlave_TXPDOMapping

0000b780     2de (0000b780)     _CANopenSlave_ODList

0000c6fa     31b (0000c6c0)     _SSIEncoderAngleValue
0000c6fc     31b (0000c6c0)     _SSICalculateAngleValue

0000c707     31c (0000c700)     _SDFormatCommondDone
0000c708     31c (0000c700)     _res
0000c709     31c (0000c700)     _br
0000c70a     31c (0000c700)     _SDSPIBusy
0000c70b     31c (0000c700)     _HistoryErrorReadLine
0000c70c     31c (0000c700)     _h

0000c740     31d (0000c740)     _SDReadBuffer

0000ccc0     333 (0000ccc0)     _fs

0000ceda     33b (0000cec0)     _HistoryErrorReadCommand
0000cedb     33b (0000cec0)     _HistoryErrorWriteCommand
0000cedd     33b (0000cec0)     _ErrorFileTextName
0000ceec     33b (0000cec0)     _HistoryErrorFileTextTemp

0000cf00     33c (0000cf00)     _LoggerSampleDoing
0000cf01     33c (0000cf00)     _LoggerSampleDone
0000cf03     33c (0000cf00)     _DataLoggerMaxLine
0000cf04     33c (0000cf00)     _DataLoggerAfterLine
0000cf05     33c (0000cf00)     _LoggerStartRecord
0000cf07     33c (0000cf00)     _LoggerReadFileNo
0000cf09     33c (0000cf00)     _LoggerTimeTransTemp
0000cf0b     33c (0000cf00)     _LoggerReadFileUpload
0000cf0c     33c (0000cf00)     _LoggerReadFileSend
0000cf0d     33c (0000cf00)     _DataLoggerBeforeTime
0000cf0e     33c (0000cf00)     _DataLoggerBeforeLine
0000cf0f     33c (0000cf00)     _DataLoggerFileCount
0000cf10     33c (0000cf00)     _DataLoggerAfterTime
0000cf20     33c (0000cf00)     _LoggerReadFileSendDataPacketIndex
0000cf36     33c (0000cf00)     _stcLoggerTriggerTime

0000cf40     33d (0000cf40)     _DataLoggerFileTextNameTemp
0000cf4a     33d (0000cf40)     _DataLoggerFileTextTemp
0000cf5e     33d (0000cf40)     _DataLoggerFileTextName

0000cf80     33e (0000cf80)     _DataLoggerDataTempName

0000cfc0     33f (0000cfc0)     _DataLoggerDataTempValue

0000d000     340 (0000d000)     _DataLoggerFileText10Line

0000d5dc     357 (0000d5c0)     _CpuTimer1
0000d5e4     357 (0000d5c0)     _CpuTimer2
0000d5ec     357 (0000d5c0)     _CpuTimer0
0000d5f4     357 (0000d5c0)     ___TI_enable_exit_profile_output
0000d5f6     357 (0000d5c0)     ___TI_cleanup_ptr
0000d5f8     357 (0000d5c0)     ___TI_dtors_ptr
0000d5fe     357 (0000d5c0)     __lock

0000d602     358 (0000d600)     _CANopenSlave_HeartBeat_TimeOut
0000d603     358 (0000d600)     _CANopenSlaveStatus
0000d604     358 (0000d600)     _CANopenSlaveNMTMessageFlag
0000d605     358 (0000d600)     _CANopenSlaveNMTMessageCommand
0000d608     358 (0000d600)     _CANopenSlave_NodeGuarding_TimeOut
0000d609     358 (0000d600)     _CANopenSlave_BootUp_Delay
0000d60a     358 (0000d600)     _CANopenSlave_HeartBeat_Delay
0000d60e     358 (0000d600)     _CANopenSlaveErrorControl
0000d617     358 (0000d600)     _CANopenSlaveTXPDOEventTimer
0000d621     358 (0000d600)     _CANopenSlaveTXPDOInhibitTimer
0000d62b     358 (0000d600)     _CANopenSlaveTXPDOEnable
0000d635     358 (0000d600)     _CANopenSlaveTXPDOSyncTimer

0000d640     359 (0000d640)     _CANopenSlave_SDOserverVar

0000d6c0     35b (0000d6c0)     _ErrorCodesTable

0000d740     35d (0000d740)     _CANopenSlave_Mapping_RXPDO

0000d840     361 (0000d840)     _CANopenSlave_Mapping_TXPDO

0000d940     365 (0000d940)     _CANopenSlaveTXMessage

0000dac0     36b (0000dac0)     _CANopenSlaveRXMessage

0000dc34     370 (0000dc00)     _StatemachineRunToEmergencyFlag
0000dc3c     370 (0000dc00)     _stcStateMachine

0000dc40     371 (0000dc40)     _ModBusTCPHandleDataDone
0000dc42     371 (0000dc40)     _SCIAReceiveDataHeartBeat
0000dc44     371 (0000dc40)     _ModBusTCPSendDataBusy
0000dc46     371 (0000dc40)     _ModBusTCPSendWaitMode
0000dc4a     371 (0000dc40)     _InternalModBusTCPConfigData

0000dc80     372 (0000dc80)     _ModBusTCPDataHold

0000dd00     374 (0000dd00)     _ModBusTCPDataInput

0000dd80     376 (0000dd80)     _ModBusTCPCommunicationData

0000e1f6     387 (0000e1c0)     __unlock
0000e1f8     387 (0000e1c0)     _SD_Stat
0000e1f9     387 (0000e1c0)     _errno

0000e21d     388 (0000e200)     _MotorParameterReceive
0000e21e     388 (0000e200)     _MotorParameterDataPacketReceiveByteIndex
0000e21f     388 (0000e200)     _MotorParameterUpload
0000e220     388 (0000e200)     _MotorParameterSend
0000e221     388 (0000e200)     _MotorParameterDataPacketReceiveNo
0000e222     388 (0000e200)     _MotorParameterSendDataPacketIndex
0000e223     388 (0000e200)     _MotorParameterHandle
0000e224     388 (0000e200)     _MotorParameterDownload

0000e280     38a (0000e280)     _stcInverterExternalInformation

0000e300     38c (0000e300)     _stcInverterInternalInformation

0000e380     38e (0000e380)     _MotorParameterReceiveDataBuffer

0000e794     39e (0000e780)     _RxPDOReceiveCheckFlag
0000e795     39e (0000e780)     _CANopenMasterStatus
0000e796     39e (0000e780)     _CANopenMasterCommunicationParameterInitDone
0000e79e     39e (0000e780)     _RxPDOReturnCount

0000e7c0     39f (0000e7c0)     _CANopenMasterSDODataType

0000e900     3a4 (0000e900)     _CANopenMasterRXMessage

0000ea80     3aa (0000ea80)     _CANopenMasterTXMessage

0000ec00     3b0 (0000ec00)     _ModBus_Hold_RW_SubIndex
0000ec01     3b0 (0000ec00)     _ModBus_Hold_RW_Index
0000ec02     3b0 (0000ec00)     _ModBusStatus
0000ec03     3b0 (0000ec00)     _ModBus_Input_Real_Time_Error_Page_Current
0000ec04     3b0 (0000ec00)     _ModBus_Hold_CANopenSlaveBaudrateOption
0000ec05     3b0 (0000ec00)     _ModBus_Hold_RW_Flag
0000ec06     3b0 (0000ec00)     _ModBus_Input_Real_Time_Error_Page_Sum
0000ec07     3b0 (0000ec00)     _ModBus_Hold_Command_Set_Motor_Parameter
0000ec08     3b0 (0000ec00)     _ModBus_Hold_Command_ProximityCalibration
0000ec09     3b0 (0000ec00)     _ModBus_Hold_Command_Test_Button_Display
0000ec0a     3b0 (0000ec00)     _ModBus_Hold_Command_CANopenSlaveBaudrateSet
0000ec0b     3b0 (0000ec00)     _ModBus_Hold_Command_ProximityReset
0000ec0c     3b0 (0000ec00)     _ModBus_Input_Code_Version_External
0000ec0d     3b0 (0000ec00)     _ModBus_Hold_Command_System_Parameter_Init
0000ec0e     3b0 (0000ec00)     _ModBus_Hold_Command_Reboot_System
0000ec0f     3b0 (0000ec00)     _ModBus_Input_Code_Version
0000ec10     3b0 (0000ec00)     _ModBus_Hold_Command_SDFormat
0000ec11     3b0 (0000ec00)     _ModBus_Hold_Command_Jog_Stop
0000ec12     3b0 (0000ec00)     _ModBus_Hold_Command_Calibrate_0
0000ec13     3b0 (0000ec00)     _ModBus_Hold_Command_Jog_Backward
0000ec14     3b0 (0000ec00)     _ModBus_Hold_Command_Jog_Speed_Level
0000ec15     3b0 (0000ec00)     _ModBus_Hold_Command_Ultracapacitor_Test
0000ec16     3b0 (0000ec00)     _ModBus_Hold_Command_Ultracapacitor_Stop_Charge
0000ec17     3b0 (0000ec00)     _ModBus_Hold_Command_Calibrate_LSA
0000ec18     3b0 (0000ec00)     _ModBus_Hold_Command_ByPass_LSB
0000ec19     3b0 (0000ec00)     _ModBus_Hold_Relay
0000ec1a     3b0 (0000ec00)     _ModBus_Hold_Command_Ultracapacitor_Discharge
0000ec1b     3b0 (0000ec00)     _ModBus_Hold_Heartbeat
0000ec1c     3b0 (0000ec00)     _ModBus_Hold_MainPowerOff
0000ec1d     3b0 (0000ec00)     _ModBus_Hold_Command_Reset
0000ec1e     3b0 (0000ec00)     _ModBus_Hold_Command_Jog_Forward
0000ec1f     3b0 (0000ec00)     _ModBus_Input_History_Error_Page_Sum
0000ec20     3b0 (0000ec00)     _ModBus_Input_History_Error_Page_Current
0000ec21     3b0 (0000ec00)     _ModBus_Hold_History_Error_Page_Up_Last
0000ec22     3b0 (0000ec00)     _ModBus_Hold_History_Error_Page_Down_Last
0000ec23     3b0 (0000ec00)     _ModBus_Hold_Real_Time_Error_Page_Up_Last
0000ec24     3b0 (0000ec00)     _ModBus_Hold_Real_Time_Error_Page_Down_Last
0000ec25     3b0 (0000ec00)     _ModBus_Input_State_Machine_Mode_Emergency_Run
0000ec26     3b0 (0000ec00)     _ModBus_Input_State_Machine_Mode_Emergency_Stop
0000ec27     3b0 (0000ec00)     _ModBus_Input_State_Machine_Mode_Normal_Stop
0000ec28     3b0 (0000ec00)     _ModBus_Input_State_Machine_Mode_Normal_Operation
0000ec29     3b0 (0000ec00)     _ModBusNodeID
0000ec2a     3b0 (0000ec00)     _ModBusConnectionFlag
0000ec2b     3b0 (0000ec00)     _ModBusEnable
0000ec2e     3b0 (0000ec00)     _ModBusError
0000ec2f     3b0 (0000ec00)     _ModBusTimeOutDelay
0000ec31     3b0 (0000ec00)     _ModBus_Hold_History_Error_Page_Up
0000ec32     3b0 (0000ec00)     _ModBus_Hold_History_Error_Page_First
0000ec33     3b0 (0000ec00)     _ModBus_Hold_History_Error_Page_Down
0000ec34     3b0 (0000ec00)     _ModBus_Input_Heartbeat
0000ec35     3b0 (0000ec00)     _ModBus_Hold_Real_Time_Error_Page_First
0000ec36     3b0 (0000ec00)     _ModBus_Hold_Command_Set_Drive_Time
0000ec37     3b0 (0000ec00)     _ModBus_Hold_Real_Time_Error_Page_Down
0000ec38     3b0 (0000ec00)     _ModBus_Hold_Real_Time_Error_Page_Up
0000ec39     3b0 (0000ec00)     _ModBus_Input_State_Machine_Mode_Reset
0000ec3a     3b0 (0000ec00)     _ModBus_Input_Calibration_Position_Done
0000ec3b     3b0 (0000ec00)     _ModBus_Input_State_Machine_Mode_Ultracapacitor_Test
0000ec3c     3b0 (0000ec00)     _ModBus_Input_State_Machine_Mode_Init
0000ec3d     3b0 (0000ec00)     _ModBus_Input_MainPowerOff
0000ec3e     3b0 (0000ec00)     _ModBus_Hold_History_Error_Reset
0000ec3f     3b0 (0000ec00)     _ModBus_Input_Calibration_Proximity_Switch_Done

0000ec40     3b1 (0000ec40)     _ModBus_Input_Calibration_Motor_Motion_Parameter_Done
0000ec41     3b1 (0000ec40)     _ModBus_Input_State_Machine_Mode_Manual
0000ec42     3b1 (0000ec40)     _ModBus_Input_Motor_Torque
0000ec44     3b1 (0000ec40)     _ModBus_Input_Motor_Current
0000ec46     3b1 (0000ec40)     _ModBus_Input_Ultracapacitor_Voltage
0000ec48     3b1 (0000ec40)     _ModBus_Input_DC_Bus_Voltage
0000ec4a     3b1 (0000ec40)     _ModBus_Input_Drive_Temperature
0000ec4c     3b1 (0000ec40)     _ModBus_Input_Actual_Speed
0000ec4e     3b1 (0000ec40)     _ModBus_Input_Actual_Position
0000ec50     3b1 (0000ec40)     _ModBus_Input_Read_Data
0000ec52     3b1 (0000ec40)     _ModBus_Input_Gear_Ratio
0000ec54     3b1 (0000ec40)     _ModBus_Hold_Calibrate_Time_Value
0000ec56     3b1 (0000ec40)     _ModBus_Hold_Write_Data
0000ec58     3b1 (0000ec40)     _ModBus_Input_Motor_Temperature
0000ec5a     3b1 (0000ec40)     _ModBus_Input_ActualTime
0000ec5c     3b1 (0000ec40)     _ModBus_Input_AI
0000ec60     3b1 (0000ec40)     _ModBus_Input_PT100
0000ec66     3b1 (0000ec40)     _ModBus_Hold_DO
0000ec6f     3b1 (0000ec40)     _ModBus_Input_Trigger_Count_Real_Time_Error

0000ec80     3b2 (0000ec80)     _ModBus_Input_Number_History_Error
0000ec8a     3b2 (0000ec80)     _ModBus_Input_Time_Low_Real_Time_Error
0000ec94     3b2 (0000ec80)     _ModBus_Input_ID_Real_Time_Error
0000ec9e     3b2 (0000ec80)     _ModBus_Input_Time_High_Real_Time_Error
0000eca8     3b2 (0000ec80)     _ModBus_Input_Reset_Time_High_History_Error
0000ecb2     3b2 (0000ec80)     _ModBus_Input_Reset_Time_Low_History_Error

0000ecc0     3b3 (0000ecc0)     _ModBus_Input_Trigger_Time_Low_History_Error
0000ecca     3b3 (0000ecc0)     _ModBus_Input_ID_History_Error
0000ecd4     3b3 (0000ecc0)     _ModBus_Input_Trigger_Time_High_History_Error
0000ecde     3b3 (0000ecc0)     _ModBus_Input_Number_Real_Time_Error
0000ece8     3b3 (0000ecc0)     _ModBusConfig

0000ed00     3b4 (0000ed00)     _ModBus_Input_DI

0000ed40     3b5 (0000ed40)     _ModBusDataInput

0000ee40     3b9 (0000ee40)     _ModBusDataHold

0000ef08     3bc (0000ef00)     _data_manipulation
0000ef09     3bc (0000ef00)     _high_capacity
0000ef0b     3bc (0000ef00)     _response
0000ef0c     3bc (0000ef00)     _crc_enabled
0000ef0d     3bc (0000ef00)     _card_status
0000ef0f     3bc (0000ef00)     _ocr_contents
0000ef14     3bc (0000ef00)     _csd_contents
0000ef24     3bc (0000ef00)     _cid_contents

0000ef46     3bd (0000ef40)     _MOTION_CONTROL_SPEED_COMPENSATION
0000ef4a     3bd (0000ef40)     _STATE_MACHINE_POSITION_KP1
0000ef4c     3bd (0000ef40)     _STATE_MACHINE_POSITION_KP2
0000ef4e     3bd (0000ef40)     _MotionControlPositionModePlanPosition
0000ef50     3bd (0000ef40)     _MOTION_CONTROL_SPEED_KI_VALUE
0000ef52     3bd (0000ef40)     _MOTION_CONTROL_SPEED_KD_VALUE
0000ef54     3bd (0000ef40)     _MOTION_CONTROL_SPEED_PLAN_COEFFICIENT
0000ef56     3bd (0000ef40)     _MOTION_CONTROL_SPEED_KP_VALUE

0000ef80     3be (0000ef80)     _stcMotionControlInternalVariable

0000f200     3c8 (0000f200)     _ParameterWRCommand

0000f716     3dc (0000f700)     _CANopenMasterErrorRXMBoxIndex
0000f717     3dc (0000f700)     _CANopenMasterRXMBoxIndex
0000f718     3dc (0000f700)     _ProgramInitSuccess
0000f719     3dc (0000f700)     _CANopenMasterRXMBoxErrorID
0000f71a     3dc (0000f700)     _CANopenSlaveRXMBoxIndex
0000f71b     3dc (0000f700)     _CANopenSlaveRXMBoxErrorID
0000f71c     3dc (0000f700)     _CANopenSlaveErrorRXMBoxIndex
0000f71d     3dc (0000f700)     _PCToolToReboot
0000f71e     3dc (0000f700)     _SDReInstallToReboot
0000f71f     3dc (0000f700)     _SDDataWRType
0000f720     3dc (0000f700)     _SDDataWRBusy
0000f721     3dc (0000f700)     _ProgramStartSuccess
0000f722     3dc (0000f700)     _SDDataInitSuccess
0000f723     3dc (0000f700)     _SDHandleFileType
0000f724     3dc (0000f700)     _SDWRRuning

0000f780     3de (0000f780)     _AWS

0000f800     3e0 (0000f800)     _InternalErrorSaveToDataLog
0000f802     3e0 (0000f800)     _ErrorDataLoggerSampleData

0000f840     3e1 (0000f840)     _DataLoggerInitSet

0000f8c1     3e3 (0000f8c0)     _OD_CANopenSlave_Controlword_Value

0000f900     3e4 (0000f900)     _SystemVariablDI

0000f9ae     3e6 (0000f980)     _RotaryDataCalculate
0000f9b6     3e6 (0000f980)     _RotaryData

0000f9c0     3e7 (0000f9c0)     _SDCardInfo

00200000    8000 (00200000)     _DataLogger

00208340    820d (00208340)     _DataLoggerUploadList

00231b80    8c6e (00231b80)     _HistoryErrorWriteChar

00233ac0    8ceb (00233ac0)     _FaultCodePropertyContent

00233bc0    8cef (00233bc0)     _FaultCodeSortHistory

00233ec0    8cfb (00233ec0)     _FaultCodePropertySetInit

00235340    8d4d (00235340)     _MotorParameterList

00312ea6    c4ba (00312e80)     _PieVectTableInit

0033fff8    cfff (0033ffc0)     _CsmPwl


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

page  address   name                                                       
----  -------   ----                                                       
0     00300002  .text                                                      
0     003109fb  C$$EXIT                                                    
0     00310356  FD$$ADD                                                    
0     003109a7  FD$$CMP                                                    
0     00310518  FD$$DIV                                                    
0     0031062b  FD$$MPY                                                    
0     00310bea  FD$$NEG                                                    
0     00310bb2  FD$$SUB                                                    
0     00310a96  FD$$TOFS                                                   
0     003105a3  FS$$DIV                                                    
0     00310b36  FS$$TOFD                                                   
0     00310ab9  I$$DIV                                                     
0     00310aca  I$$MOD                                                     
0     003109d1  L$$DIV                                                     
0     003109e0  L$$MOD                                                     
0     00310b52  L$$TOFD                                                    
0     00310b87  LL$$AND                                                    
0     00310afb  LL$$CMP                                                    
0     0030fb87  LL$$DIV                                                    
0     0030fbc3  LL$$MOD                                                    
0     00310b8f  LL$$OR                                                     
0     00310b97  LL$$XOR                                                    
0     00310bcc  U$$DIV                                                     
0     00310bd1  U$$MOD                                                     
0     003109ee  UL$$DIV                                                    
0     003109f5  UL$$MOD                                                    
0     00310b0d  ULL$$CMP                                                   
0     0030fbfd  ULL$$DIV                                                   
0     0030fc2c  ULL$$MOD                                                   
0     0030e213  _ADCControlICConfig                                        
0     0030ece5  _ADCINT_ISR                                                
0     0030e38d  _ADCToAICurrent                                            
0     0030e2f9  _ADCToAIVoltage                                            
0     0030e258  _ADCToPT100Temperature                                     
0     0030d2d1  _ADC_Collect                                               
0     00380080  _ADC_cal                                                   
0     0000f780  _AWS                                                       
0     0030a012  _AWSBackground                                             
0     0030a785  _AWSDataInteractionInput                                   
0     0030a791  _AWSDataInteractionOutput                                  
0     00309f4b  _AWSInit                                                   
0     0030a765  _AWSInverterCommunicationRun                               
0     0030a510  _AWSLibFunctionRun                                         
0     0030a771  _AWSMasterCommunicationRun                                 
0     0030a8f1  _AWSRTCReadMPUTransformation                               
0     0030a893  _AWSRTCReadTimeTransformation                              
0     0030a79f  _AWSSDWRPriorityJudge                                      
0     0030a843  _AWSSDWRTimeOut                                            
0     00310934  _AWSSpecialVariableAssignment                              
0     0030a251  _AWSSystemReboot                                           
0     00310923  _AWSTaskCycle100MS                                         
0     00310917  _AWSTaskCycle10MS                                          
0     00310913  _AWSTaskCycle2MS                                           
0     0030a256  _AWSVariableInit                                           
0     0030a26b  _AWSVariableInputRun                                       
0     0030a4c0  _AWSVariableOutputRun                                      
1     00000b00  _AdcMirror                                                 
1     00007100  _AdcRegs                                                   
0     0030d2e2  _CANA_Receive_With_Master                                  
0     0030d41b  _CANB_Receive_With_Inverter                                
0     003014ce  _CANOpenMasterCalibratePosition                            
0     00300538  _CANOpenMasterMessageInit                                  
0     0030001d  _CANOpenMasterNMTControl                                   
0     003006ca  _CANOpenMasterODInit                                       
0     00300190  _CANOpenMasterReceivePDOData                               
0     003032b7  _CANOpenMasterSDOCommand                                   
0     00303309  _CANOpenMasterSDOConfirm                                   
0     00300f41  _CANOpenMasterSDOParameterInit                             
0     00303231  _CANOpenMasterSDOWR                                        
0     003015ea  _CANOpenMasterSDOWRData                                    
0     003004a1  _CANOpenMasterSendPDOData                                  
0     003005a9  _CANOpenMasterSetup                                        
0     00300002  _CANOpenMasterStatusInit                                   
0     0030334b  _CANOpenMaster_TXMessageToBuffers                          
0     00308c73  _CANOpenSlaveBaudrateSet                                   
0     00308cb9  _CANOpenSlaveComminit                                      
0     00309391  _CANOpenSlaveCommunicationParameterChange                  
0     00308c7e  _CANOpenSlaveDataInit                                      
0     00309379  _CANOpenSlaveErrorReport                                   
0     0030930c  _CANOpenSlaveFindEntryInOD                                 
0     00308cae  _CANOpenSlaveInit                                          
0     003083ae  _CANOpenSlaveNMTControl                                    
0     00308843  _CANOpenSlaveReceiveData                                   
0     003089fa  _CANOpenSlaveSendData                                      
0     003091fa  _CANOpenSlaveSetup                                         
0     003083a8  _CANOpenSlaveStatusInit                                    
0     00309399  _CANOpenSlave_ErrorDataFrameResultInterruptStop            
0     003093c3  _CANOpenSlave_TXMessageToBuffers                           
0     0000e796  _CANopenMasterCommunicationParameterInitDone               
0     0000f716  _CANopenMasterErrorRXMBoxIndex                             
0     0000f719  _CANopenMasterRXMBoxErrorID                                
0     0000f717  _CANopenMasterRXMBoxIndex                                  
0     0000e900  _CANopenMasterRXMessage                                    
0     0000e7c0  _CANopenMasterSDODataType                                  
0     0000e795  _CANopenMasterStatus                                       
0     0000ea80  _CANopenMasterTXMessage                                    
0     0000d60e  _CANopenSlaveErrorControl                                  
0     0000f71c  _CANopenSlaveErrorRXMBoxIndex                              
0     0000d605  _CANopenSlaveNMTMessageCommand                             
0     0000d604  _CANopenSlaveNMTMessageFlag                                
0     0000f71b  _CANopenSlaveRXMBoxErrorID                                 
0     0000f71a  _CANopenSlaveRXMBoxIndex                                   
0     0000dac0  _CANopenSlaveRXMessage                                     
0     0000d603  _CANopenSlaveStatus                                        
0     0000d940  _CANopenSlaveTXMessage                                     
0     0000d62b  _CANopenSlaveTXPDOEnable                                   
0     0000d617  _CANopenSlaveTXPDOEventTimer                               
0     0000d621  _CANopenSlaveTXPDOInhibitTimer                             
0     0000d635  _CANopenSlaveTXPDOSyncTimer                                
0     0000d609  _CANopenSlave_BootUp_Delay                                 
0     0000d60a  _CANopenSlave_HeartBeat_Delay                              
0     0000d602  _CANopenSlave_HeartBeat_TimeOut                            
0     0000d740  _CANopenSlave_Mapping_RXPDO                                
0     0000d840  _CANopenSlave_Mapping_TXPDO                                
0     0000d608  _CANopenSlave_NodeGuarding_TimeOut                         
0     0000b780  _CANopenSlave_ODList                                       
0     0000b366  _CANopenSlave_ODNoOfElements                               
0     0000d640  _CANopenSlave_SDOserverVar                                 
0     00309ea1  _CRC16                                                     
0     003106ef  _ConfigCpuTimer                                            
0     0000d5ec  _CpuTimer0                                                 
1     00000c00  _CpuTimer0Regs                                             
0     0000d5dc  _CpuTimer1                                                 
1     00000c08  _CpuTimer1Regs                                             
0     0000d5e4  _CpuTimer2                                                 
1     00000c10  _CpuTimer2Regs                                             
1     0033fff8  _CsmPwl                                                    
1     00000ae0  _CsmRegs                                                   
0     0030fa3d  _CsmUnlock                                                 
0     0030ec0c  _DATALOG_ISR                                               
0     0030ee07  _DINTCH1_ISR                                               
0     0030ee11  _DINTCH2_ISR                                               
0     0030ee1b  _DINTCH3_ISR                                               
0     0030ee25  _DINTCH4_ISR                                               
0     0030ee2f  _DINTCH5_ISR                                               
0     0030ee39  _DINTCH6_ISR                                               
0     0000ff1b  _DSP28x_usDelay                                            
0     00200000  _DataLogger                                                
0     0000cf04  _DataLoggerAfterLine                                       
0     0000cf10  _DataLoggerAfterTime                                       
0     0000cf0e  _DataLoggerBeforeLine                                      
0     0000cf0d  _DataLoggerBeforeTime                                      
0     0000cf80  _DataLoggerDataTempName                                    
0     0000cfc0  _DataLoggerDataTempValue                                   
0     0000cf0f  _DataLoggerFileCount                                       
0     0000d000  _DataLoggerFileText10Line                                  
0     0000cf5e  _DataLoggerFileTextName                                    
0     0000cf40  _DataLoggerFileTextNameTemp                                
0     0000cf4a  _DataLoggerFileTextTemp                                    
0     0000f840  _DataLoggerInitSet                                         
0     0000cf03  _DataLoggerMaxLine                                         
0     00208340  _DataLoggerUploadList                                      
1     00000880  _DevEmuRegs                                                
0     0030f95e  _DisableDog                                                
1     00001000  _DmaRegs                                                   
0     0030ee93  _ECAN0INTA_ISR                                             
0     0030eea7  _ECAN0INTB_ISR                                             
0     0030ee9d  _ECAN1INTA_ISR                                             
0     0030eeb1  _ECAN1INTB_ISR                                             
0     0030ed7b  _ECAP1_INT_ISR                                             
0     0030ed85  _ECAP2_INT_ISR                                             
0     0030ed8f  _ECAP3_INT_ISR                                             
0     0030ed99  _ECAP4_INT_ISR                                             
0     0030eda3  _ECAP5_INT_ISR                                             
0     0030edad  _ECAP6_INT_ISR                                             
1     00006040  _ECanaLAMRegs                                              
1     000060c0  _ECanaMOTORegs                                             
1     00006080  _ECanaMOTSRegs                                             
1     00006100  _ECanaMboxes                                               
1     00006000  _ECanaRegs                                                 
1     00006240  _ECanbLAMRegs                                              
1     000062c0  _ECanbMOTORegs                                             
1     00006280  _ECanbMOTSRegs                                             
1     00006300  _ECanbMboxes                                               
1     00006200  _ECanbRegs                                                 
1     00006a00  _ECap1Regs                                                 
1     00006a20  _ECap2Regs                                                 
1     00006a40  _ECap3Regs                                                 
1     00006a60  _ECap4Regs                                                 
1     00006a80  _ECap5Regs                                                 
1     00006aa0  _ECap6Regs                                                 
0     0030ef01  _EMPTY_ISR                                                 
0     0030ec20  _EMUINT_ISR                                                
0     0030ed3f  _EPWM1_INT_ISR                                             
0     0030ed03  _EPWM1_TZINT_ISR                                           
0     0030ed49  _EPWM2_INT_ISR                                             
0     0030ed0d  _EPWM2_TZINT_ISR                                           
0     0030d4fc  _EPWM3_INT_CLK                                             
0     0030ed53  _EPWM3_INT_ISR                                             
0     0030ed17  _EPWM3_TZINT_ISR                                           
0     0030ed5d  _EPWM4_INT_ISR                                             
0     0030ed21  _EPWM4_TZINT_ISR                                           
0     0030ed67  _EPWM5_INT_ISR                                             
0     0030ed2b  _EPWM5_TZINT_ISR                                           
0     0030ed71  _EPWM6_INT_ISR                                             
0     0030ed35  _EPWM6_TZINT_ISR                                           
1     00006800  _EPwm1Regs                                                 
1     00006840  _EPwm2Regs                                                 
1     00006880  _EPwm3Regs                                                 
1     000068c0  _EPwm4Regs                                                 
1     00006900  _EPwm5Regs                                                 
1     00006940  _EPwm6Regs                                                 
0     0030edb7  _EQEP1_INT_ISR                                             
0     0030edc1  _EQEP2_INT_ISR                                             
1     00006b00  _EQep1Regs                                                 
1     00006b40  _EQep2Regs                                                 
0     00310a43  _EnableInterrupts                                          
0     0000d6c0  _ErrorCodesTable                                           
0     0000f802  _ErrorDataLoggerSampleData                                 
0     0000cedd  _ErrorFileTextName                                         
0     0030f7ff  _ErrorInit                                                 
0     0030f894  _ErrorRun                                                  
0     00008008  _ExternalParametercount                                    
0     00304c0f  _FaultCodeAutomaticReset                                   
0     003037f2  _FaultCodeErrorConfig                                      
0     00303885  _FaultCodeErrorConfigEnd                                   
0     0030385c  _FaultCodeErrorConfigSHEInternalToOD                       
0     00303b26  _FaultCodeErrorConfigSHEInternalToODAll                    
0     00303824  _FaultCodeErrorConfigSHEODToInternal                       
0     00303913  _FaultCodeErrorConfigSHEODToInternalAll                    
0     00304002  _FaultCodeGetCode                                          
0     00304f87  _FaultCodeGetHistoryResetTime                              
0     00303d6d  _FaultCodeGetSafetyChainStatus                             
0     00303d69  _FaultCodeGetStatus                                        
0     00303ff6  _FaultCodeGetTriggerStatus                                 
0     0000a0c0  _FaultCodeHandleDelay                                      
0     0030f948  _FaultCodeInit                                             
0     00304fb5  _FaultCodeInternalErrorTrigger                             
0     003033a5  _FaultCodeInternalInit                                     
0     00303744  _FaultCodeInternalRun                                      
0     00304e22  _FaultCodeInverterResetCommandReset                        
0     00303fb3  _FaultCodeManualReset                                      
0     00303f5a  _FaultCodeOncelReset                                       
0     0000ac00  _FaultCodeProperty                                         
0     00233ac0  _FaultCodePropertyContent                                  
0     00233ec0  _FaultCodePropertySetInit                                  
0     00009f00  _FaultCodePropertyWRCommand                                
0     00303d39  _FaultCodePropertyWRRun                                    
0     00304013  _FaultCodePropertyWRSD                                     
0     00304bb7  _FaultCodePropertyWRSDReset                                
0     0000a400  _FaultCodeRealTime                                         
0     00009e45  _FaultCodeSDHandleFlag                                     
0     00009e4b  _FaultCodeSafetyChainStatus                                
0     00303d71  _FaultCodeSet                                              
0     00233bc0  _FaultCodeSortHistory                                      
0     00303fe2  _FaultCodeSortHistoryReset                                 
0     00304eaa  _FaultCodeSortHistoryTrigger                               
0     0000a800  _FaultCodeSortRealTime                                     
0     00304f07  _FaultCodeSortRealTimeReset                                
0     00304e3d  _FaultCodeSortRealTimeTrigger                              
0     00009e80  _FaultCodeSpecialErrorJudgementCondition                   
0     00009e44  _FaultCodeSystemStatus                                     
0     00304bbb  _FaultCodeTrigger                                          
0     00009e42  _FaultCodeWRSDRun                                          
0     0030b847  _FileCharToFloat                                           
0     0030b8c5  _FileCharToInt                                             
0     0030b8f6  _FileCharToLong                                            
0     0030b627  _FileFloatToChar                                           
0     0030b73c  _FileIntToChar                                             
0     0030b7d6  _FileLongToChar                                            
0     0030b935  _FileParameterCharacterClassify                            
0     0030b60a  _FileSDFormat                                              
0     0030b26b  _FileSDInit                                                
0     0030b41b  _FileSDRead                                                
0     0030b282  _FileSDWrite                                               
0     0030b619  _FileStringByteLength                                      
1     00000a80  _FlashRegs                                                 
1     00006f80  _GpioCtrlRegs                                              
1     00006fc0  _GpioDataRegs                                              
1     00006fe0  _GpioIntRegs                                               
0     0030b9a6  _HistoryErrorCharToValue                                   
0     0000ceec  _HistoryErrorFileTextTemp                                  
0     0030ef22  _HistoryErrorInit                                          
0     0030ef29  _HistoryErrorRead                                          
0     0000ceda  _HistoryErrorReadCommand                                   
0     0000c70b  _HistoryErrorReadLine                                      
0     0030efb6  _HistoryErrorWrite                                         
0     00231b80  _HistoryErrorWriteChar                                     
0     0000cedb  _HistoryErrorWriteCommand                                  
0     0030fb4c  _I2CByteRead                                               
0     0030fb1c  _I2CByteWrite                                              
0     0030fb79  _I2CCommunicationDelay                                     
0     0030ee43  _I2CINT1A_ISR                                              
0     0030ee4d  _I2CINT2A_ISR                                              
0     0030faea  _I2C_Ack                                                   
0     0030fa7e  _I2C_Init                                                  
0     0030fb03  _I2C_NAck                                                  
0     0030fa9a  _I2C_Start                                                 
0     0030fab1  _I2C_Stop                                                  
0     0030fac6  _I2C_Wait_Ack                                              
1     00007900  _I2caRegs                                                  
0     0030ec34  _ILLEGAL_ISR                                               
0     0030ebf8  _INT13_ISR                                                 
0     0030ec02  _INT14_ISR                                                 
0     0030e0fb  _IOControlICGPIOConfig                                     
0     0000b30a  _IOControlICInformation                                    
0     0030df76  _IOInit                                                    
0     0030df7b  _IOInputRun                                                
0     0030e061  _IOOutputRun                                               
0     00310852  _InitAdc                                                   
0     003106ae  _InitCpuTimers                                             
0     0030f551  _InitECan                                                  
0     0030f688  _InitECanGpio                                              
0     0030f557  _InitECana                                                 
0     0030f68d  _InitECanaGpio                                             
0     0030f608  _InitECanb                                                 
0     0030f69d  _InitECanbGpio                                             
0     00310795  _InitEPwm                                                  
0     00310799  _InitEPwm1Gpio                                             
0     003107ab  _InitEPwm2Gpio                                             
0     003107bd  _InitEPwm3Gpio                                             
0     00310796  _InitEPwmGpio                                              
0     003107ca  _InitEPwmSyncGpio                                          
0     0000ff00  _InitFlash                                                 
0     0030f9b7  _InitPeripheralClocks                                      
0     00310a24  _InitPieCtrl                                               
0     00310adb  _InitPieVectTable                                          
0     0030f966  _InitPll                                                   
0     0031097c  _InitSci                                                   
0     0031097d  _InitSciGpio                                               
0     00310982  _InitSciaGpio                                              
0     00310997  _InitScibGpio                                              
0     00310950  _InitSpi                                                   
0     00310951  _InitSpiGpio                                               
0     00310954  _InitSpiaGpio                                              
0     0030f94b  _InitSysCtrl                                               
0     003107de  _InitTzGpio                                                
0     0030fd87  _InitXintf                                                 
0     0030fe27  _InitXintf16Gpio                                           
0     0030fde0  _InitXintf32Gpio                                           
0     0000f800  _InternalErrorSaveToDataLog                                
0     0000dc4a  _InternalModBusTCPConfigData                               
0     00008180  _InternalParameterWRCommand                                
0     00008007  _InternalParametercount                                    
0     003081ad  _InverterCheckError                                        
0     0030785c  _InverterControlCalculateCapacitanceReset                  
0     003076ef  _InverterControlCalculateCapacitanceValue                  
0     0030764c  _InverterControlCharge                                     
0     0030760e  _InverterControlDisCharge                                  
0     0030793a  _InverterControlEmergency                                  
0     0030788d  _InverterControlHubSpeedCalculate                          
0     00307015  _InverterControlInit                                       
0     0030816b  _InverterControlInverterTimeCalibrate                      
0     00308191  _InverterControlInverterTimeCalibrateTimeOut               
0     00307da9  _InverterControlMotorParameterDownload                     
0     003080f8  _InverterControlMotorParameterDownloadTimeOut              
0     00307948  _InverterControlMotorParameterSelect                       
0     003079b6  _InverterControlMotorParameterUpload                       
0     003080be  _InverterControlMotorParameterUploadTimeOut                
0     003073ab  _InverterDateToSeconds                                     
0     00307285  _InverterInformationWriteToControlICRun                    
0     003070f3  _InverterInformationWriteToInverterRun                     
0     0030703e  _InverterMotionCommandWriteToInverter                      
0     0030831f  _InverterMotorParameterCharToInt                           
0     00308228  _InverterNixieTubeDisplay                                  
0     003074e3  _InverterSecondsToDate                                     
0     0030eef7  _LUF_ISR                                                   
0     0030eeed  _LVF_ISR                                                   
0     0000cf07  _LoggerReadFileNo                                          
0     0000cf0c  _LoggerReadFileSend                                        
0     0000cf20  _LoggerReadFileSendDataPacketIndex                         
0     0000cf0b  _LoggerReadFileUpload                                      
0     0030c372  _LoggerReadSD                                              
0     0030c3b6  _LoggerReadTimeout                                         
0     0000cf00  _LoggerSampleDoing                                         
0     0000cf01  _LoggerSampleDone                                          
0     0000cf05  _LoggerStartRecord                                         
0     0000cf09  _LoggerTimeTransTemp                                       
0     0030bbb1  _LoggerWriteInit                                           
0     0030bc6e  _LoggerWriteRecord                                         
0     0030bfb6  _LoggerWriteSD                                             
0     0000ef46  _MOTION_CONTROL_SPEED_COMPENSATION                         
0     0000ef52  _MOTION_CONTROL_SPEED_KD_VALUE                             
0     0000ef50  _MOTION_CONTROL_SPEED_KI_VALUE                             
0     0000ef56  _MOTION_CONTROL_SPEED_KP_VALUE                             
0     0000ef54  _MOTION_CONTROL_SPEED_PLAN_COEFFICIENT                     
0     0030e4e8  _MPU_Get_Accelerometer                                     
0     0030e615  _MPU_Get_Angle                                             
0     0030e4c2  _MPU_Get_Gyroscope                                         
0     0030e492  _MPU_Get_Temperature                                       
0     0030e42a  _MPU_Init                                                  
0     0030e59f  _MPU_Read_Byte                                             
0     0030e53f  _MPU_Read_Len                                              
0     0030e472  _MPU_Run                                                   
0     0030e5c8  _MPU_Set_Accel_Fsr                                         
0     0030e5be  _MPU_Set_Gyro_Fsr                                          
0     0030e5d2  _MPU_Set_LPF                                               
0     0030e5f6  _MPU_Set_Rate                                              
0     0030e57a  _MPU_Write_Byte                                            
0     0030e50e  _MPU_Write_Len                                             
0     0030edf3  _MRINTA_ISR                                                
0     0030eddf  _MRINTB_ISR                                                
0     0030edfd  _MXINTA_ISR                                                
0     0030ede9  _MXINTB_ISR                                                
0     0030e86b  _ManualCalibrationPosition                                 
0     0030e8cf  _ManualCalibrationProximity0                               
0     0030e975  _ManualCalibrationProximity0Reset                          
0     0030e9cc  _ManualCalibrationProximity1                               
0     0030ea72  _ManualCalibrationProximity1Reset                          
0     0030ead3  _ManualCalibrationProximity2                               
0     0030eb79  _ManualCalibrationProximity2Reset                          
0     0030e86a  _ManualInit                                                
0     0030ebda  _ManualJogMove                                             
0     0030e8a2  _ManualPositionStatusCheck                                 
0     0030e981  _ManualProximity0StatusCheck                               
0     0030ea7e  _ManualProximity1StatusCheck                               
0     0030eb85  _ManualProximity2StatusCheck                               
1     00005000  _McbspaRegs                                                
1     00005040  _McbspbRegs                                                
0     00310b9f  _MemCopy                                                   
0     0030dd11  _ModBusChannelDataAssignment                               
0     0030df53  _ModBusChannelDataFistInDataHandle                         
0     0030deab  _ModBusChannelDataInit                                     
0     0000ece8  _ModBusConfig                                              
0     0000ec2a  _ModBusConnectionFlag                                      
0     0000ee40  _ModBusDataHold                                            
0     0000ed40  _ModBusDataInput                                           
0     0000ec2b  _ModBusEnable                                              
0     0000ec2e  _ModBusError                                               
0     0030db11  _ModBusErrorInformationDisplay                             
0     0030daae  _ModBusInit                                                
0     0000ec29  _ModBusNodeID                                              
0     0030daee  _ModBusRun                                                 
0     0000ec02  _ModBusStatus                                              
0     0030dafb  _ModBusStatusCheck                                         
0     0000dd80  _ModBusTCPCommunicationData                                
0     0000dc80  _ModBusTCPDataHold                                         
0     0000dd00  _ModBusTCPDataInput                                        
0     0000dc40  _ModBusTCPHandleDataDone                                   
0     0000dc44  _ModBusTCPSendDataBusy                                     
0     0000dc46  _ModBusTCPSendWaitMode                                     
0     0030973d  _ModBusTCPSlaveCommunicationDataHandle                     
0     0030941d  _ModBusTCPSlaveConfig                                      
0     0030944c  _ModBusTCPSlaveInit                                        
0     0000ec2f  _ModBusTimeOutDelay                                        
0     0000ec04  _ModBus_Hold_CANopenSlaveBaudrateOption                    
0     0000ec54  _ModBus_Hold_Calibrate_Time_Value                          
0     0000ec18  _ModBus_Hold_Command_ByPass_LSB                            
0     0000ec0a  _ModBus_Hold_Command_CANopenSlaveBaudrateSet               
0     0000ec12  _ModBus_Hold_Command_Calibrate_0                           
0     0000ec17  _ModBus_Hold_Command_Calibrate_LSA                         
0     0000ec13  _ModBus_Hold_Command_Jog_Backward                          
0     0000ec1e  _ModBus_Hold_Command_Jog_Forward                           
0     0000ec14  _ModBus_Hold_Command_Jog_Speed_Level                       
0     0000ec11  _ModBus_Hold_Command_Jog_Stop                              
0     0000ec08  _ModBus_Hold_Command_ProximityCalibration                  
0     0000ec0b  _ModBus_Hold_Command_ProximityReset                        
0     0000ec0e  _ModBus_Hold_Command_Reboot_System                         
0     0000ec1d  _ModBus_Hold_Command_Reset                                 
0     0000ec10  _ModBus_Hold_Command_SDFormat                              
0     0000ec36  _ModBus_Hold_Command_Set_Drive_Time                        
0     0000ec07  _ModBus_Hold_Command_Set_Motor_Parameter                   
0     0000ec0d  _ModBus_Hold_Command_System_Parameter_Init                 
0     0000ec09  _ModBus_Hold_Command_Test_Button_Display                   
0     0000ec1a  _ModBus_Hold_Command_Ultracapacitor_Discharge              
0     0000ec16  _ModBus_Hold_Command_Ultracapacitor_Stop_Charge            
0     0000ec15  _ModBus_Hold_Command_Ultracapacitor_Test                   
0     0000ec66  _ModBus_Hold_DO                                            
0     0000ec1b  _ModBus_Hold_Heartbeat                                     
0     0000ec33  _ModBus_Hold_History_Error_Page_Down                       
0     0000ec22  _ModBus_Hold_History_Error_Page_Down_Last                  
0     0000ec32  _ModBus_Hold_History_Error_Page_First                      
0     0000ec31  _ModBus_Hold_History_Error_Page_Up                         
0     0000ec21  _ModBus_Hold_History_Error_Page_Up_Last                    
0     0000ec3e  _ModBus_Hold_History_Error_Reset                           
0     0000ec1c  _ModBus_Hold_MainPowerOff                                  
0     0000ec05  _ModBus_Hold_RW_Flag                                       
0     0000ec01  _ModBus_Hold_RW_Index                                      
0     0000ec00  _ModBus_Hold_RW_SubIndex                                   
0     0000ec37  _ModBus_Hold_Real_Time_Error_Page_Down                     
0     0000ec24  _ModBus_Hold_Real_Time_Error_Page_Down_Last                
0     0000ec35  _ModBus_Hold_Real_Time_Error_Page_First                    
0     0000ec38  _ModBus_Hold_Real_Time_Error_Page_Up                       
0     0000ec23  _ModBus_Hold_Real_Time_Error_Page_Up_Last                  
0     0000ec19  _ModBus_Hold_Relay                                         
0     0000ec56  _ModBus_Hold_Write_Data                                    
0     0000ec5c  _ModBus_Input_AI                                           
0     0000ec5a  _ModBus_Input_ActualTime                                   
0     0000ec4e  _ModBus_Input_Actual_Position                              
0     0000ec4c  _ModBus_Input_Actual_Speed                                 
0     0000ec40  _ModBus_Input_Calibration_Motor_Motion_Parameter_Done      
0     0000ec3a  _ModBus_Input_Calibration_Position_Done                    
0     0000ec3f  _ModBus_Input_Calibration_Proximity_Switch_Done            
0     0000ec0f  _ModBus_Input_Code_Version                                 
0     0000ec0c  _ModBus_Input_Code_Version_External                        
0     0000ec48  _ModBus_Input_DC_Bus_Voltage                               
0     0000ed00  _ModBus_Input_DI                                           
0     0000ec4a  _ModBus_Input_Drive_Temperature                            
0     0000ec52  _ModBus_Input_Gear_Ratio                                   
0     0000ec34  _ModBus_Input_Heartbeat                                    
0     0000ec20  _ModBus_Input_History_Error_Page_Current                   
0     0000ec1f  _ModBus_Input_History_Error_Page_Sum                       
0     0000ecca  _ModBus_Input_ID_History_Error                             
0     0000ec94  _ModBus_Input_ID_Real_Time_Error                           
0     0000ec3d  _ModBus_Input_MainPowerOff                                 
0     0000ec44  _ModBus_Input_Motor_Current                                
0     0000ec58  _ModBus_Input_Motor_Temperature                            
0     0000ec42  _ModBus_Input_Motor_Torque                                 
0     0000ec80  _ModBus_Input_Number_History_Error                         
0     0000ecde  _ModBus_Input_Number_Real_Time_Error                       
0     0000ec60  _ModBus_Input_PT100                                        
0     0000ec50  _ModBus_Input_Read_Data                                    
0     0000ec03  _ModBus_Input_Real_Time_Error_Page_Current                 
0     0000ec06  _ModBus_Input_Real_Time_Error_Page_Sum                     
0     0000eca8  _ModBus_Input_Reset_Time_High_History_Error                
0     0000ecb2  _ModBus_Input_Reset_Time_Low_History_Error                 
0     0000ec25  _ModBus_Input_State_Machine_Mode_Emergency_Run             
0     0000ec26  _ModBus_Input_State_Machine_Mode_Emergency_Stop            
0     0000ec3c  _ModBus_Input_State_Machine_Mode_Init                      
0     0000ec41  _ModBus_Input_State_Machine_Mode_Manual                    
0     0000ec28  _ModBus_Input_State_Machine_Mode_Normal_Operation          
0     0000ec27  _ModBus_Input_State_Machine_Mode_Normal_Stop               
0     0000ec39  _ModBus_Input_State_Machine_Mode_Reset                     
0     0000ec3b  _ModBus_Input_State_Machine_Mode_Ultracapacitor_Test       
0     0000ec9e  _ModBus_Input_Time_High_Real_Time_Error                    
0     0000ec8a  _ModBus_Input_Time_Low_Real_Time_Error                     
0     0000ec6f  _ModBus_Input_Trigger_Count_Real_Time_Error                
0     0000ecd4  _ModBus_Input_Trigger_Time_High_History_Error              
0     0000ecc0  _ModBus_Input_Trigger_Time_Low_History_Error               
0     0000ec46  _ModBus_Input_Ultracapacitor_Voltage                       
0     00309ec7  _ModbusTCPSlaveInvalidDataAbandon                          
0     0030949b  _ModbusTCPSlaveReceive                                     
0     0030970d  _ModbusTCPSlaveSend                                        
0     0030cb60  _MotionControlInit                                         
0     0000ef4e  _MotionControlPositionModePlanPosition                     
0     0030cd8a  _MotionControlRun                                          
0     0030cc27  _MotionControlSpeedPID                                     
0     0030cba9  _MotionControlSpeedPlan                                    
0     0030cb6e  _MotionControlVariableFilter                               
0     0000e21e  _MotorParameterDataPacketReceiveByteIndex                  
0     0000e221  _MotorParameterDataPacketReceiveNo                         
0     0000e224  _MotorParameterDownload                                    
0     0000e223  _MotorParameterHandle                                      
0     00235340  _MotorParameterList                                        
0     0000e21d  _MotorParameterReceive                                     
0     0000e380  _MotorParameterReceiveDataBuffer                           
0     0000e220  _MotorParameterSend                                        
0     0000e222  _MotorParameterSendDataPacketIndex                         
0     0000e21f  _MotorParameterUpload                                      
0     0030ec2a  _NMI_ISR                                                   
0     0000b365  _OD_CANopenSlave_AccelerationDimensionIndex                
0     0000b36a  _OD_CANopenSlave_AccelerationNotationIndex                 
0     0000b389  _OD_CANopenSlave_AxleCabTemp                               
0     0000b394  _OD_CANopenSlave_BKOffDelay                                
0     0000b396  _OD_CANopenSlave_BKOnDelay                                 
0     0000b39e  _OD_CANopenSlave_BKSwitch                                  
0     0000b37d  _OD_CANopenSlave_CapCabTemp                                
0     0000b343  _OD_CANopenSlave_ChargCtrlMod                              
0     0000b340  _OD_CANopenSlave_ChargCtrlReg                              
0     0000b356  _OD_CANopenSlave_ChargDefectCode                           
0     0000b34c  _OD_CANopenSlave_ChargIFeedback                            
0     0000b341  _OD_CANopenSlave_ChargISet                                 
0     0000b358  _OD_CANopenSlave_ChargIgbtTemp                             
0     0000b355  _OD_CANopenSlave_ChargSysStCode                            
0     0000b346  _OD_CANopenSlave_ChargVSet                                 
0     0000b345  _OD_CANopenSlave_ChargVol                                  
0     0000b418  _OD_CANopenSlave_ChargerErrIGBTOverTemper                  
0     0000b3f0  _OD_CANopenSlave_ChargerErrLowVol                          
0     0000b510  _OD_CANopenSlave_ChargerErrOverVol                         
0     0000b408  _OD_CANopenSlave_ChargerErrShortCircuit                    
0     0000b3a2  _OD_CANopenSlave_CommunicationCyclePeriod                  
0     0000b3da  _OD_CANopenSlave_ConsumerHeartBeatTime                     
0     0000b378  _OD_CANopenSlave_Controlword                               
0     0000f8c1  _OD_CANopenSlave_Controlword_Value                         
0     0000b363  _OD_CANopenSlave_CurrentActualValue                        
0     0000b35e  _OD_CANopenSlave_DCLinkCircuitVoltage                      
0     0000b34b  _OD_CANopenSlave_DIErrorOutSideChoose                      
0     0000b3bc  _OD_CANopenSlave_DeviceType                                
0     0000b3b4  _OD_CANopenSlave_DigitalInputs                             
0     0000b3d4  _OD_CANopenSlave_DigitalOutputs                            
0     0000b359  _OD_CANopenSlave_DischargeAccTime                          
0     0000b350  _OD_CANopenSlave_DischargeTime                             
0     0000b35c  _OD_CANopenSlave_DischargeTimeOutSlope                     
0     0000b35b  _OD_CANopenSlave_DischargeTimeThreshold                    
0     0000b3aa  _OD_CANopenSlave_EmergencyCOBID                            
0     0000b3ba  _OD_CANopenSlave_EncAUserActPos                            
0     0000b3c6  _OD_CANopenSlave_EncBUserActPos                            
0     0000b399  _OD_CANopenSlave_ErrRst                                    
0     0000b3d1  _OD_CANopenSlave_ErrorBehavior                             
0     0000b3ae  _OD_CANopenSlave_ErrorCode1                                
0     0000b3b8  _OD_CANopenSlave_ErrorCode2                                
0     0000b387  _OD_CANopenSlave_ErrorRegister                             
0     0000b393  _OD_CANopenSlave_EscRemote                                 
0     0000b357  _OD_CANopenSlave_FltPraOfCurrent                           
0     0000b36c  _OD_CANopenSlave_FltPraOfDCVol                             
0     0000b36b  _OD_CANopenSlave_FltPraOfVelocity                          
0     0000b3ce  _OD_CANopenSlave_GearRatioMotorRevolutions                 
0     0000b385  _OD_CANopenSlave_GuardTime                                 
0     0000b364  _OD_CANopenSlave_HomingSpeeds                              
0     0000b37b  _OD_CANopenSlave_Hub_HumiOrTemp                            
0     0000b342  _OD_CANopenSlave_IOControl                                 
0     0000b580  _OD_CANopenSlave_IdentifyObject                            
0     0000b39d  _OD_CANopenSlave_IgbtSafeTemp                              
0     0000b38b  _OD_CANopenSlave_IgbtTemp                                  
0     0000b38f  _OD_CANopenSlave_InhibitTimeEmergency                      
0     0000b386  _OD_CANopenSlave_LifeTimeFactor                            
0     0000b38d  _OD_CANopenSlave_LimitT                                    
0     0000b38e  _OD_CANopenSlave_LimitV                                    
0     0000b5c0  _OD_CANopenSlave_ManufacturerDeviceName                    
0     0000b5d4  _OD_CANopenSlave_ManufacturerHardwareVersion               
0     0000b59c  _OD_CANopenSlave_ManufacturerSoftwareVersion               
0     0000b3a8  _OD_CANopenSlave_ManufacturerStatusRegister                
0     0000b380  _OD_CANopenSlave_MaxI                                      
0     0000b35d  _OD_CANopenSlave_MaxMotorSpeed                             
0     0000b37e  _OD_CANopenSlave_MaxT                                      
0     0000b397  _OD_CANopenSlave_ModeCtrl                                  
0     0000b362  _OD_CANopenSlave_ModesOfOperation                          
0     0000b361  _OD_CANopenSlave_ModesOfOperationDisplay                   
0     0000b38c  _OD_CANopenSlave_MotoHumidEn                               
0     0000b37f  _OD_CANopenSlave_MotoSafeTemp                              
0     0000b388  _OD_CANopenSlave_MotoTemp                                  
0     0000b3b6  _OD_CANopenSlave_MotorAngelNew                             
0     0000b35a  _OD_CANopenSlave_MotorPos                                  
0     0000b352  _OD_CANopenSlave_MotorPosNum                               
0     0000b39a  _OD_CANopenSlave_MotorPosRst                               
0     0000b367  _OD_CANopenSlave_MotorType                                 
0     0000b37c  _OD_CANopenSlave_OverITotal                                
0     0000b382  _OD_CANopenSlave_PPV                                       
0     0000b34e  _OD_CANopenSlave_PortOutData                               
0     0000b3ac  _OD_CANopenSlave_PositionActualValue                       
0     0000b3de  _OD_CANopenSlave_PositionControlParameterSetManufacturer   
0     0000b3d7  _OD_CANopenSlave_PositionEncoderResolutionEncoderIncrements
0     0000b58a  _OD_CANopenSlave_PredefineErrorField                       
0     0000b390  _OD_CANopenSlave_ProducerHeartbeatTime                     
0     0000b35f  _OD_CANopenSlave_ProfileAcceleration                       
0     0000b368  _OD_CANopenSlave_ProfileDeceleration                       
0     0000b360  _OD_CANopenSlave_ProfileVelocity                           
0     0000b3c8  _OD_CANopenSlave_RWParaComm                                
0     0000b3ca  _OD_CANopenSlave_RWParaData                                
0     0000b680  _OD_CANopenSlave_RXPDOMapping                              
0     0000b600  _OD_CANopenSlave_RXPDOParameter                            
0     0000b381  _OD_CANopenSlave_RatedCurrent                              
0     0000b3e4  _OD_CANopenSlave_SDOParameter                              
0     0000b39b  _OD_CANopenSlave_SDownTime                                 
0     0000b39c  _OD_CANopenSlave_SUpTime                                   
0     0000b377  _OD_CANopenSlave_SafeCloseACCPD                            
0     0000b371  _OD_CANopenSlave_SafeCloseDecPD                            
0     0000b392  _OD_CANopenSlave_SafeCloseDownTime                         
0     0000b395  _OD_CANopenSlave_SafeCloseUpTime                           
0     0000b351  _OD_CANopenSlave_SafeDIDly                                 
0     0000b376  _OD_CANopenSlave_SavePara                                  
0     0000b3c0  _OD_CANopenSlave_SecCoderAngle                             
0     0000b353  _OD_CANopenSlave_SecCoderDir                               
0     0000b34f  _OD_CANopenSlave_SecPos                                    
0     0000b354  _OD_CANopenSlave_SecPosNum                                 
0     0000b38a  _OD_CANopenSlave_ServiceTimeDelay                          
0     0000b460  _OD_CANopenSlave_ServoErr380VErr                           
0     0000b478  _OD_CANopenSlave_ServoErrBK24VLost                         
0     0000b420  _OD_CANopenSlave_ServoErrBrokenCircuit                     
0     0000b4d0  _OD_CANopenSlave_ServoErrCANOpenLineOff                    
0     0000b560  _OD_CANopenSlave_ServoErrChargerComErr                     
0     0000b470  _OD_CANopenSlave_ServoErrCurrentOver                       
0     0000b558  _OD_CANopenSlave_ServoErrDITrigErr                         
0     0000b3f8  _OD_CANopenSlave_ServoErrDYErr                             
0     0000b490  _OD_CANopenSlave_ServoErrDischargeFail                     
0     0000b448  _OD_CANopenSlave_ServoErrHardOverCurrent                   
0     0000b440  _OD_CANopenSlave_ServoErrHardOverCurrent1                  
0     0000b400  _OD_CANopenSlave_ServoErrHardOverVol                       
0     0000b540  _OD_CANopenSlave_ServoErrIGBTLineOff                       
0     0000b4d8  _OD_CANopenSlave_ServoErrIGBTOverTemper                    
0     0000b480  _OD_CANopenSlave_ServoErrIner24VLost                       
0     0000b458  _OD_CANopenSlave_ServoErrLimit96                           
0     0000b4b8  _OD_CANopenSlave_ServoErrLowVol                            
0     0000b568  _OD_CANopenSlave_ServoErrMotoLineOff                       
0     0000b4c8  _OD_CANopenSlave_ServoErrMotoOverTemper                    
0     0000b530  _OD_CANopenSlave_ServoErrNULL1                             
0     0000b450  _OD_CANopenSlave_ServoErrNULL10                            
0     0000b438  _OD_CANopenSlave_ServoErrNULL11                            
0     0000b528  _OD_CANopenSlave_ServoErrNULL2                             
0     0000b4f8  _OD_CANopenSlave_ServoErrNULL3                             
0     0000b500  _OD_CANopenSlave_ServoErrNULL4                             
0     0000b4e8  _OD_CANopenSlave_ServoErrNULL5                             
0     0000b4f0  _OD_CANopenSlave_ServoErrNULL6                             
0     0000b518  _OD_CANopenSlave_ServoErrNULL7                             
0     0000b430  _OD_CANopenSlave_ServoErrNULL8                             
0     0000b428  _OD_CANopenSlave_ServoErrNULL9                             
0     0000b4a0  _OD_CANopenSlave_ServoErrOuter24VLost                      
0     0000b498  _OD_CANopenSlave_ServoErrOverCurrent                       
0     0000b4e0  _OD_CANopenSlave_ServoErrOverLoad                          
0     0000b4b0  _OD_CANopenSlave_ServoErrOverVol                           
0     0000b538  _OD_CANopenSlave_ServoErrPaddleOver                        
0     0000b4a8  _OD_CANopenSlave_ServoErrPosLost                           
0     0000b550  _OD_CANopenSlave_ServoErrPositionOver                      
0     0000b4c0  _OD_CANopenSlave_ServoErrSSILineOff                        
0     0000b548  _OD_CANopenSlave_ServoErrSaferErr                          
0     0000b468  _OD_CANopenSlave_ServoErrShortCircuit                      
0     0000b410  _OD_CANopenSlave_ServoErrSoftOverCurrent                   
0     0000b508  _OD_CANopenSlave_ServoErrSoftOverVol                       
0     0000b520  _OD_CANopenSlave_ServoErrTotal                             
0     0000b488  _OD_CANopenSlave_ServoErrVelocityOver                      
0     0000b36d  _OD_CANopenSlave_SnOff                                     
0     0000b36e  _OD_CANopenSlave_SnOn                                      
0     0000b379  _OD_CANopenSlave_SpeedOverproofT                           
0     0000b373  _OD_CANopenSlave_Statusword                                
0     0000b570  _OD_CANopenSlave_StoreParameters                           
0     0000b3a4  _OD_CANopenSlave_SyncCOBID                                 
0     0000b3a6  _OD_CANopenSlave_SynchronousWindowLength                   
0     0000b383  _OD_CANopenSlave_TSet                                      
0     0000b700  _OD_CANopenSlave_TXPDOMapping                              
0     0000b640  _OD_CANopenSlave_TXPDOParameter                            
0     0000b3a0  _OD_CANopenSlave_TargetPosition                            
0     0000b3cc  _OD_CANopenSlave_TargetVelocity                            
0     0000b374  _OD_CANopenSlave_TorqueProfileType                         
0     0000b384  _OD_CANopenSlave_UserCVol                                  
0     0000b3be  _OD_CANopenSlave_UserPara1                                 
0     0000b36f  _OD_CANopenSlave_UserPara10                                
0     0000b3c4  _OD_CANopenSlave_UserPara2                                 
0     0000b3c2  _OD_CANopenSlave_UserPara3                                 
0     0000b34d  _OD_CANopenSlave_UserPara4                                 
0     0000b348  _OD_CANopenSlave_UserPara5                                 
0     0000b347  _OD_CANopenSlave_UserPara6                                 
0     0000b34a  _OD_CANopenSlave_UserPara7                                 
0     0000b349  _OD_CANopenSlave_UserPara8                                 
0     0000b370  _OD_CANopenSlave_UserPara9                                 
0     0000b391  _OD_CANopenSlave_VDirMod                                   
0     0000b37a  _OD_CANopenSlave_VPi                                       
0     0000b369  _OD_CANopenSlave_VelocityActualValue                       
0     0000b3ea  _OD_CANopenSlave_VelocityControlParameter                  
0     0000b3b2  _OD_CANopenSlave_VelocityDemandValue                       
0     0000b3b0  _OD_CANopenSlave_VelocitySensorActualValue                 
0     0000b372  _OD_CANopenSlave_VlRampFunctionTime                        
0     0000b375  _OD_CANopenSlave_VlSlowDownTime                            
0     0000b344  _OD_CANopenSlave_VoltageOfUser                             
0     0000b398  _OD_CANopenSlave_WorkMod                                   
0     0000f71d  _PCToolToReboot                                            
0     00008003  _PICANOpenSlaveBaudrateOption                              
0     0030ef0e  _PIE_RESERVED                                              
0     00008014  _PIEncoderCalibrationReferenceValue                        
0     0000800b  _PIEncoderCalibrationStatus                                
0     00008002  _PIMotorParameterVersion                                   
0     00008006  _PINextDataLoggerNubmer                                    
0     00008010  _PIProximitySwitch0CalibrationBackwardTriggerAngle         
0     00008018  _PIProximitySwitch0CalibrationForwardTriggerAngle          
0     00008000  _PIProximitySwitch0CalibrationStatus                       
0     00008016  _PIProximitySwitch1CalibrationBackwardTriggerAngle         
0     0000800e  _PIProximitySwitch1CalibrationForwardTriggerAngle          
0     00008001  _PIProximitySwitch1CalibrationStatus                       
0     0000801c  _PIProximitySwitch2CalibrationBackwardTriggerAngle         
0     00008012  _PIProximitySwitch2CalibrationForwardTriggerAngle          
0     0000800c  _PIProximitySwitch2CalibrationStatus                       
0     0000801e  _PISSIEncoderCalibrationReferenceValue                     
0     00008004  _PIUltracapacitorCalculateTestCount                        
0     00008020  _PIUltracapacitorCalculateTestTimeLast                     
0     0000801a  _PIUltracapacitorCalculateValue                            
0     0000800a  _ParameterConfigFinishFlag                                 
0     00008680  _ParameterExternalWRSetInit                                
0     003107f6  _ParameterInit                                             
0     00008200  _ParameterInternalWRSetInit                                
0     00310849  _ParameterRun                                              
0     0000800d  _ParameterSDHandleFlag                                     
0     0000f200  _ParameterWRCommand                                        
0     0030a906  _ParameterWRInit                                           
0     0030ab9c  _ParameterWRRun                                            
0     0030adf1  _ParameterWRSD                                             
0     0030adec  _ParameterWRSDConfigEnd                                    
0     0030ad59  _ParameterWRSDConfigFloat                                  
0     0030ac42  _ParameterWRSDConfigInteger                                
0     0030accd  _ParameterWRSDConfigLong                                   
0     0030b267  _ParameterWRSDReset                                        
0     00008009  _ParameterWRSDRun                                          
1     00000ce0  _PieCtrlRegs                                               
1     00000d00  _PieVectTable                                              
0     00312ea6  _PieVectTableInit                                          
0     0000f718  _ProgramInitSuccess                                        
0     0000f721  _ProgramStartSuccess                                       
0     0000b337  _RTCDataTime                                               
0     0030f22d  _RTCInit                                                   
0     0030f26a  _RTCRead                                                   
0     0030f2d3  _RTCReadDateTime                                           
0     0030f2a8  _RTCWrite                                                  
0     0030f348  _RTCWriteDateTime                                          
0     0030ec16  _RTOSINT_ISR                                               
0     00310c32  _RamfuncsLoadEnd                                           
0     00310c13  _RamfuncsLoadStart                                         
0     0000ff00  _RamfuncsRunStart                                          
0     0000f9b6  _RotaryData                                                
0     0000f9ae  _RotaryDataCalculate                                       
0     0000b336  _RtcReadValue                                              
0     0000e794  _RxPDOReceiveCheckFlag                                     
0     0000e79e  _RxPDOReturnCount                                          
0     0000dc42  _SCIAReceiveDataHeartBeat                                  
0     00309f19  _SCIA_Init                                                 
0     0030d1a0  _SCIA_Receive                                              
0     00309f32  _SCIB_Init                                                 
0     0030d29c  _SCIB_Receive                                              
0     0030ee6b  _SCIRXINTA_ISR                                             
0     0030ee7f  _SCIRXINTB_ISR                                             
0     0030ee57  _SCIRXINTC_ISR                                             
0     0030ee75  _SCITXINTA_ISR                                             
0     0030ee89  _SCITXINTB_ISR                                             
0     0030ee61  _SCITXINTC_ISR                                             
0     0000f9c0  _SDCardInfo                                                
0     0000f722  _SDDataInitSuccess                                         
0     0000f720  _SDDataWRBusy                                              
0     0000f71f  _SDDataWRType                                              
0     0000c707  _SDFormatCommondDone                                       
0     0000f723  _SDHandleFileType                                          
0     0000f71e  _SDReInstallToReboot                                       
0     0000c740  _SDReadBuffer                                              
0     0000c70a  _SDSPIBusy                                                 
0     0000f724  _SDWRRuning                                                
0     00310729  _SD_Init                                                   
0     0031073f  _SD_ReadBlock                                              
0     00310754  _SD_ReadMultiBlocks                                        
0     0000e1f8  _SD_Stat                                                   
0     0031076a  _SD_WriteBlock                                             
0     0031077f  _SD_WriteMultiBlocks                                       
0     0030ecbd  _SEQ1INT_ISR                                               
0     0030ecc7  _SEQ2INT_ISR                                               
0     0030edcb  _SPIRXINTA_ISR                                             
0     0030edd5  _SPITXINTA_ISR                                             
0     0000c6fc  _SSICalculateAngleValue                                    
0     00310458  _SSICalibrationHardware                                    
0     0031045d  _SSICalibrationSoftware                                    
0     00310476  _SSIDirection                                              
0     0000c6fa  _SSIEncoderAngleValue                                      
0     003103f2  _SSIInit                                                   
0     00310430  _SSIRun                                                    
0     0000ef4a  _STATE_MACHINE_POSITION_KP1                                
0     0000ef4c  _STATE_MACHINE_POSITION_KP2                                
1     00007050  _SciaRegs                                                  
1     00007750  _ScibRegs                                                  
1     00007770  _ScicRegs                                                  
0     0030f954  _ServiceDog                                                
1     00007040  _SpiaRegs                                                  
0     0030c481  _StateMachineInit                                          
0     0030c48d  _StateMachineRun                                           
0     0030c9b6  _StateMachine_Status_EmergencyRun                          
0     0030c75c  _StateMachine_Status_Init                                  
0     0030c95f  _StateMachine_Status_Position                              
0     0030c78e  _StateMachine_Status_Stop                                  
0     0030c4ae  _StateMachine_Status_Switch                                
0     0030c915  _StateMachine_Status_Velocity                              
0     0000dc34  _StatemachineRunToEmergencyFlag                            
1     00007010  _SysCtrlRegs                                               
0     0000f900  _SystemVariablDI                                           
0     0030da8d  _SystemVariableIOTriggerDelay                              
0     0030d5da  _SystemVariableInit                                        
0     0030d98c  _SystemVariableRun100MS                                    
0     0030d5e2  _SystemVariableRun10MS                                     
0     0030da89  _SystemVariableRun1S                                       
0     0030ecef  _TINT0_ISR                                                 
0     0030d156  _TaskCycle100MS                                            
0     0030d10f  _TaskCycle10MS                                             
0     0030d0a2  _TaskCycle2MS                                              
0     0030ec9f  _USER10_ISR                                                
0     0030eca9  _USER11_ISR                                                
0     0030ecb3  _USER12_ISR                                                
0     0030ec45  _USER1_ISR                                                 
0     0030ec4f  _USER2_ISR                                                 
0     0030ec59  _USER3_ISR                                                 
0     0030ec63  _USER4_ISR                                                 
0     0030ec6d  _USER5_ISR                                                 
0     0030ec77  _USER6_ISR                                                 
0     0030ec81  _USER7_ISR                                                 
0     0030ec8b  _USER8_ISR                                                 
0     0030ec95  _USER9_ISR                                                 
0     0030ecf9  _WAKEINT_ISR                                               
0     0030ecd1  _XINT1_ISR                                                 
0     0030ecdb  _XINT2_ISR                                                 
0     0030eebb  _XINT3_ISR                                                 
0     0030eec5  _XINT4_ISR                                                 
0     0030eecf  _XINT5_ISR                                                 
0     0030eed9  _XINT6_ISR                                                 
0     0030eee3  _XINT7_ISR                                                 
1     00007070  _XIntruptRegs                                              
1     00000b20  _XintfRegs                                                 
1     00000800  __STACK_END                                                
abs   00000400  __STACK_SIZE                                               
0     0000d5f6  ___TI_cleanup_ptr                                          
0     0000d5f8  ___TI_dtors_ptr                                            
0     0000d5f4  ___TI_enable_exit_profile_output                           
abs   ffffffff  ___TI_pprof_out_hndl                                       
abs   ffffffff  ___TI_prof_data_size                                       
abs   ffffffff  ___TI_prof_data_start                                      
abs   ffffffff  ___binit__                                                 
abs   ffffffff  ___c_args__                                                
0     00310c32  ___cinit__                                                 
0     00310c13  ___etext__                                                 
abs   ffffffff  ___pinit__                                                 
0     00300002  ___text__                                                  
0     00310b6e  __args_main                                                
0     0000d5fe  __lock                                                     
0     00310be9  __nop                                                      
0     00310be5  __register_lock                                            
0     00310be1  __register_unlock                                          
1     00000400  __stack                                                    
0     00310485  __system_post_cinit                                        
0     00310c11  __system_pre_init                                          
0     0000e1f6  __unlock                                                   
0     003109fb  _abort                                                     
0     0030fe7d  _asin                                                      
0     0030fe7d  _asinf                                                     
0     003101e5  _atan                                                      
0     00310044  _atan2                                                     
0     00310044  _atan2f                                                    
0     003101e5  _atanf                                                     
0     0000c709  _br                                                        
0     003108aa  _c_int00                                                   
0     0000ef0d  _card_status                                               
0     0000ef24  _cid_contents                                              
0     00305541  _clust2sect                                                
0     00310a72  _copy_in                                                   
0     0000ef0c  _crc_enabled                                               
0     0000ef14  _csd_contents                                              
0     0000ef08  _data_manipulation                                         
0     0030f6ad  _disk_initialize                                           
0     0030f710  _disk_ioctl                                                
0     0030f6dd  _disk_read                                                 
0     0030f6d0  _disk_status                                               
0     0030f6f8  _disk_write                                                
0     0000e1f9  _errno                                                     
0     003109fd  _exit                                                      
0     003068d9  _f_chmod                                                   
0     00306374  _f_close                                                   
0     0030657f  _f_getfree                                                 
0     00306e8a  _f_gets                                                    
0     00306736  _f_linkInit                                                
0     00306389  _f_lseek                                                   
0     003067ac  _f_mkdir                                                   
0     00306a3a  _f_mkfs                                                    
0     00305f02  _f_mount                                                   
0     00305f26  _f_open                                                    
0     003064b5  _f_opendir                                                 
0     00306ef5  _f_printf                                                  
0     00306ec0  _f_putc                                                    
0     00306edc  _f_puts                                                    
0     00306069  _f_read                                                    
0     00306519  _f_readdir                                                 
0     00306962  _f_rename                                                  
0     00306381  _f_simple_close                                            
0     00306555  _f_stat                                                    
0     003062c4  _f_sync                                                    
0     00306637  _f_truncate                                                
0     003066aa  _f_unlink                                                  
0     00306918  _f_utime                                                   
0     00306180  _f_write                                                   
0     0000ccc0  _fs                                                        
0     0030555b  _get_fat                                                   
0     0030f7d6  _get_fattime                                               
0     0000c70c  _h                                                         
0     0000ef09  _high_capacity                                             
0     00310900  _main                                                      
0     00310b19  _memcpy                                                    
0     00310bc0  _memset                                                    
0     0000ef0f  _ocr_contents                                              
0     003055f6  _put_fat                                                   
0     0000c708  _res                                                       
0     0000ef0b  _response                                                  
0     0030ef18  _rsvd_ISR                                                  
0     0030f3d9  _sd_card_insertion                                         
0     00310164  _sd_cid_csd_response                                       
0     00310330  _sd_command_response                                       
0     00310301  _sd_crc7                                                   
0     0030ffeb  _sd_data_response                                          
0     0031034d  _sd_error                                                  
0     0030f3f6  _sd_initialization                                         
0     00310147  _sd_ocr_response                                           
0     0030ff6e  _sd_read_block                                             
0     0030ff9e  _sd_read_multiple_block                                    
0     00310118  _sd_read_register                                          
0     003101ae  _sd_send_status                                            
0     0030f460  _sd_version1_initialization                                
0     0030f4ca  _sd_version2_initialization                                
0     0030fc8e  _sd_write_block                                            
0     0030fd13  _sd_write_data                                             
0     0030fcc5  _sd_write_multiple_block                                   
0     0030f3cc  _spi_initialization                                        
0     003102af  _spi_xmit_byte                                             
0     003102bd  _spi_xmit_command                                          
0     00310a4c  _sqrt                                                      
0     00310a4c  _sqrtf                                                     
0     00310486  _sqrtl                                                     
0     0000e280  _stcInverterExternalInformation                            
0     0000e300  _stcInverterInternalInformation                            
0     0000cf36  _stcLoggerTriggerTime                                      
0     0000ef80  _stcMotionControlInternalVariable                          
0     0000dc3c  _stcStateMachine                                           
0     00310bf3  _strcat                                                    
0     00310bd7  _strcmp                                                    
0     00310c0c  _strcpy                                                    
0     00310c04  _strlen                                                    
abs   ffffffff  binit                                                      
0     00310c32  cinit                                                      
0     00300000  code_start                                                 
0     00310c13  etext                                                      
abs   ffffffff  pinit                                                      


GLOBAL SYMBOLS: SORTED BY Symbol Address 

page  address   name                                                       
----  -------   ----                                                       
0     00008000  _PIProximitySwitch0CalibrationStatus                       
0     00008001  _PIProximitySwitch1CalibrationStatus                       
0     00008002  _PIMotorParameterVersion                                   
0     00008003  _PICANOpenSlaveBaudrateOption                              
0     00008004  _PIUltracapacitorCalculateTestCount                        
0     00008006  _PINextDataLoggerNubmer                                    
0     00008007  _InternalParametercount                                    
0     00008008  _ExternalParametercount                                    
0     00008009  _ParameterWRSDRun                                          
0     0000800a  _ParameterConfigFinishFlag                                 
0     0000800b  _PIEncoderCalibrationStatus                                
0     0000800c  _PIProximitySwitch2CalibrationStatus                       
0     0000800d  _ParameterSDHandleFlag                                     
0     0000800e  _PIProximitySwitch1CalibrationForwardTriggerAngle          
0     00008010  _PIProximitySwitch0CalibrationBackwardTriggerAngle         
0     00008012  _PIProximitySwitch2CalibrationForwardTriggerAngle          
0     00008014  _PIEncoderCalibrationReferenceValue                        
0     00008016  _PIProximitySwitch1CalibrationBackwardTriggerAngle         
0     00008018  _PIProximitySwitch0CalibrationForwardTriggerAngle          
0     0000801a  _PIUltracapacitorCalculateValue                            
0     0000801c  _PIProximitySwitch2CalibrationBackwardTriggerAngle         
0     0000801e  _PISSIEncoderCalibrationReferenceValue                     
0     00008020  _PIUltracapacitorCalculateTestTimeLast                     
0     00008180  _InternalParameterWRCommand                                
0     00008200  _ParameterInternalWRSetInit                                
0     00008680  _ParameterExternalWRSetInit                                
0     00009e42  _FaultCodeWRSDRun                                          
0     00009e44  _FaultCodeSystemStatus                                     
0     00009e45  _FaultCodeSDHandleFlag                                     
0     00009e4b  _FaultCodeSafetyChainStatus                                
0     00009e80  _FaultCodeSpecialErrorJudgementCondition                   
0     00009f00  _FaultCodePropertyWRCommand                                
0     0000a0c0  _FaultCodeHandleDelay                                      
0     0000a400  _FaultCodeRealTime                                         
0     0000a800  _FaultCodeSortRealTime                                     
0     0000ac00  _FaultCodeProperty                                         
0     0000b30a  _IOControlICInformation                                    
0     0000b336  _RtcReadValue                                              
0     0000b337  _RTCDataTime                                               
0     0000b340  _OD_CANopenSlave_ChargCtrlReg                              
0     0000b341  _OD_CANopenSlave_ChargISet                                 
0     0000b342  _OD_CANopenSlave_IOControl                                 
0     0000b343  _OD_CANopenSlave_ChargCtrlMod                              
0     0000b344  _OD_CANopenSlave_VoltageOfUser                             
0     0000b345  _OD_CANopenSlave_ChargVol                                  
0     0000b346  _OD_CANopenSlave_ChargVSet                                 
0     0000b347  _OD_CANopenSlave_UserPara6                                 
0     0000b348  _OD_CANopenSlave_UserPara5                                 
0     0000b349  _OD_CANopenSlave_UserPara8                                 
0     0000b34a  _OD_CANopenSlave_UserPara7                                 
0     0000b34b  _OD_CANopenSlave_DIErrorOutSideChoose                      
0     0000b34c  _OD_CANopenSlave_ChargIFeedback                            
0     0000b34d  _OD_CANopenSlave_UserPara4                                 
0     0000b34e  _OD_CANopenSlave_PortOutData                               
0     0000b34f  _OD_CANopenSlave_SecPos                                    
0     0000b350  _OD_CANopenSlave_DischargeTime                             
0     0000b351  _OD_CANopenSlave_SafeDIDly                                 
0     0000b352  _OD_CANopenSlave_MotorPosNum                               
0     0000b353  _OD_CANopenSlave_SecCoderDir                               
0     0000b354  _OD_CANopenSlave_SecPosNum                                 
0     0000b355  _OD_CANopenSlave_ChargSysStCode                            
0     0000b356  _OD_CANopenSlave_ChargDefectCode                           
0     0000b357  _OD_CANopenSlave_FltPraOfCurrent                           
0     0000b358  _OD_CANopenSlave_ChargIgbtTemp                             
0     0000b359  _OD_CANopenSlave_DischargeAccTime                          
0     0000b35a  _OD_CANopenSlave_MotorPos                                  
0     0000b35b  _OD_CANopenSlave_DischargeTimeThreshold                    
0     0000b35c  _OD_CANopenSlave_DischargeTimeOutSlope                     
0     0000b35d  _OD_CANopenSlave_MaxMotorSpeed                             
0     0000b35e  _OD_CANopenSlave_DCLinkCircuitVoltage                      
0     0000b35f  _OD_CANopenSlave_ProfileAcceleration                       
0     0000b360  _OD_CANopenSlave_ProfileVelocity                           
0     0000b361  _OD_CANopenSlave_ModesOfOperationDisplay                   
0     0000b362  _OD_CANopenSlave_ModesOfOperation                          
0     0000b363  _OD_CANopenSlave_CurrentActualValue                        
0     0000b364  _OD_CANopenSlave_HomingSpeeds                              
0     0000b365  _OD_CANopenSlave_AccelerationDimensionIndex                
0     0000b366  _CANopenSlave_ODNoOfElements                               
0     0000b367  _OD_CANopenSlave_MotorType                                 
0     0000b368  _OD_CANopenSlave_ProfileDeceleration                       
0     0000b369  _OD_CANopenSlave_VelocityActualValue                       
0     0000b36a  _OD_CANopenSlave_AccelerationNotationIndex                 
0     0000b36b  _OD_CANopenSlave_FltPraOfVelocity                          
0     0000b36c  _OD_CANopenSlave_FltPraOfDCVol                             
0     0000b36d  _OD_CANopenSlave_SnOff                                     
0     0000b36e  _OD_CANopenSlave_SnOn                                      
0     0000b36f  _OD_CANopenSlave_UserPara10                                
0     0000b370  _OD_CANopenSlave_UserPara9                                 
0     0000b371  _OD_CANopenSlave_SafeCloseDecPD                            
0     0000b372  _OD_CANopenSlave_VlRampFunctionTime                        
0     0000b373  _OD_CANopenSlave_Statusword                                
0     0000b374  _OD_CANopenSlave_TorqueProfileType                         
0     0000b375  _OD_CANopenSlave_VlSlowDownTime                            
0     0000b376  _OD_CANopenSlave_SavePara                                  
0     0000b377  _OD_CANopenSlave_SafeCloseACCPD                            
0     0000b378  _OD_CANopenSlave_Controlword                               
0     0000b379  _OD_CANopenSlave_SpeedOverproofT                           
0     0000b37a  _OD_CANopenSlave_VPi                                       
0     0000b37b  _OD_CANopenSlave_Hub_HumiOrTemp                            
0     0000b37c  _OD_CANopenSlave_OverITotal                                
0     0000b37d  _OD_CANopenSlave_CapCabTemp                                
0     0000b37e  _OD_CANopenSlave_MaxT                                      
0     0000b37f  _OD_CANopenSlave_MotoSafeTemp                              
0     0000b380  _OD_CANopenSlave_MaxI                                      
0     0000b381  _OD_CANopenSlave_RatedCurrent                              
0     0000b382  _OD_CANopenSlave_PPV                                       
0     0000b383  _OD_CANopenSlave_TSet                                      
0     0000b384  _OD_CANopenSlave_UserCVol                                  
0     0000b385  _OD_CANopenSlave_GuardTime                                 
0     0000b386  _OD_CANopenSlave_LifeTimeFactor                            
0     0000b387  _OD_CANopenSlave_ErrorRegister                             
0     0000b388  _OD_CANopenSlave_MotoTemp                                  
0     0000b389  _OD_CANopenSlave_AxleCabTemp                               
0     0000b38a  _OD_CANopenSlave_ServiceTimeDelay                          
0     0000b38b  _OD_CANopenSlave_IgbtTemp                                  
0     0000b38c  _OD_CANopenSlave_MotoHumidEn                               
0     0000b38d  _OD_CANopenSlave_LimitT                                    
0     0000b38e  _OD_CANopenSlave_LimitV                                    
0     0000b38f  _OD_CANopenSlave_InhibitTimeEmergency                      
0     0000b390  _OD_CANopenSlave_ProducerHeartbeatTime                     
0     0000b391  _OD_CANopenSlave_VDirMod                                   
0     0000b392  _OD_CANopenSlave_SafeCloseDownTime                         
0     0000b393  _OD_CANopenSlave_EscRemote                                 
0     0000b394  _OD_CANopenSlave_BKOffDelay                                
0     0000b395  _OD_CANopenSlave_SafeCloseUpTime                           
0     0000b396  _OD_CANopenSlave_BKOnDelay                                 
0     0000b397  _OD_CANopenSlave_ModeCtrl                                  
0     0000b398  _OD_CANopenSlave_WorkMod                                   
0     0000b399  _OD_CANopenSlave_ErrRst                                    
0     0000b39a  _OD_CANopenSlave_MotorPosRst                               
0     0000b39b  _OD_CANopenSlave_SDownTime                                 
0     0000b39c  _OD_CANopenSlave_SUpTime                                   
0     0000b39d  _OD_CANopenSlave_IgbtSafeTemp                              
0     0000b39e  _OD_CANopenSlave_BKSwitch                                  
0     0000b3a0  _OD_CANopenSlave_TargetPosition                            
0     0000b3a2  _OD_CANopenSlave_CommunicationCyclePeriod                  
0     0000b3a4  _OD_CANopenSlave_SyncCOBID                                 
0     0000b3a6  _OD_CANopenSlave_SynchronousWindowLength                   
0     0000b3a8  _OD_CANopenSlave_ManufacturerStatusRegister                
0     0000b3aa  _OD_CANopenSlave_EmergencyCOBID                            
0     0000b3ac  _OD_CANopenSlave_PositionActualValue                       
0     0000b3ae  _OD_CANopenSlave_ErrorCode1                                
0     0000b3b0  _OD_CANopenSlave_VelocitySensorActualValue                 
0     0000b3b2  _OD_CANopenSlave_VelocityDemandValue                       
0     0000b3b4  _OD_CANopenSlave_DigitalInputs                             
0     0000b3b6  _OD_CANopenSlave_MotorAngelNew                             
0     0000b3b8  _OD_CANopenSlave_ErrorCode2                                
0     0000b3ba  _OD_CANopenSlave_EncAUserActPos                            
0     0000b3bc  _OD_CANopenSlave_DeviceType                                
0     0000b3be  _OD_CANopenSlave_UserPara1                                 
0     0000b3c0  _OD_CANopenSlave_SecCoderAngle                             
0     0000b3c2  _OD_CANopenSlave_UserPara3                                 
0     0000b3c4  _OD_CANopenSlave_UserPara2                                 
0     0000b3c6  _OD_CANopenSlave_EncBUserActPos                            
0     0000b3c8  _OD_CANopenSlave_RWParaComm                                
0     0000b3ca  _OD_CANopenSlave_RWParaData                                
0     0000b3cc  _OD_CANopenSlave_TargetVelocity                            
0     0000b3ce  _OD_CANopenSlave_GearRatioMotorRevolutions                 
0     0000b3d1  _OD_CANopenSlave_ErrorBehavior                             
0     0000b3d4  _OD_CANopenSlave_DigitalOutputs                            
0     0000b3d7  _OD_CANopenSlave_PositionEncoderResolutionEncoderIncrements
0     0000b3da  _OD_CANopenSlave_ConsumerHeartBeatTime                     
0     0000b3de  _OD_CANopenSlave_PositionControlParameterSetManufacturer   
0     0000b3e4  _OD_CANopenSlave_SDOParameter                              
0     0000b3ea  _OD_CANopenSlave_VelocityControlParameter                  
0     0000b3f0  _OD_CANopenSlave_ChargerErrLowVol                          
0     0000b3f8  _OD_CANopenSlave_ServoErrDYErr                             
0     0000b400  _OD_CANopenSlave_ServoErrHardOverVol                       
0     0000b408  _OD_CANopenSlave_ChargerErrShortCircuit                    
0     0000b410  _OD_CANopenSlave_ServoErrSoftOverCurrent                   
0     0000b418  _OD_CANopenSlave_ChargerErrIGBTOverTemper                  
0     0000b420  _OD_CANopenSlave_ServoErrBrokenCircuit                     
0     0000b428  _OD_CANopenSlave_ServoErrNULL9                             
0     0000b430  _OD_CANopenSlave_ServoErrNULL8                             
0     0000b438  _OD_CANopenSlave_ServoErrNULL11                            
0     0000b440  _OD_CANopenSlave_ServoErrHardOverCurrent1                  
0     0000b448  _OD_CANopenSlave_ServoErrHardOverCurrent                   
0     0000b450  _OD_CANopenSlave_ServoErrNULL10                            
0     0000b458  _OD_CANopenSlave_ServoErrLimit96                           
0     0000b460  _OD_CANopenSlave_ServoErr380VErr                           
0     0000b468  _OD_CANopenSlave_ServoErrShortCircuit                      
0     0000b470  _OD_CANopenSlave_ServoErrCurrentOver                       
0     0000b478  _OD_CANopenSlave_ServoErrBK24VLost                         
0     0000b480  _OD_CANopenSlave_ServoErrIner24VLost                       
0     0000b488  _OD_CANopenSlave_ServoErrVelocityOver                      
0     0000b490  _OD_CANopenSlave_ServoErrDischargeFail                     
0     0000b498  _OD_CANopenSlave_ServoErrOverCurrent                       
0     0000b4a0  _OD_CANopenSlave_ServoErrOuter24VLost                      
0     0000b4a8  _OD_CANopenSlave_ServoErrPosLost                           
0     0000b4b0  _OD_CANopenSlave_ServoErrOverVol                           
0     0000b4b8  _OD_CANopenSlave_ServoErrLowVol                            
0     0000b4c0  _OD_CANopenSlave_ServoErrSSILineOff                        
0     0000b4c8  _OD_CANopenSlave_ServoErrMotoOverTemper                    
0     0000b4d0  _OD_CANopenSlave_ServoErrCANOpenLineOff                    
0     0000b4d8  _OD_CANopenSlave_ServoErrIGBTOverTemper                    
0     0000b4e0  _OD_CANopenSlave_ServoErrOverLoad                          
0     0000b4e8  _OD_CANopenSlave_ServoErrNULL5                             
0     0000b4f0  _OD_CANopenSlave_ServoErrNULL6                             
0     0000b4f8  _OD_CANopenSlave_ServoErrNULL3                             
0     0000b500  _OD_CANopenSlave_ServoErrNULL4                             
0     0000b508  _OD_CANopenSlave_ServoErrSoftOverVol                       
0     0000b510  _OD_CANopenSlave_ChargerErrOverVol                         
0     0000b518  _OD_CANopenSlave_ServoErrNULL7                             
0     0000b520  _OD_CANopenSlave_ServoErrTotal                             
0     0000b528  _OD_CANopenSlave_ServoErrNULL2                             
0     0000b530  _OD_CANopenSlave_ServoErrNULL1                             
0     0000b538  _OD_CANopenSlave_ServoErrPaddleOver                        
0     0000b540  _OD_CANopenSlave_ServoErrIGBTLineOff                       
0     0000b548  _OD_CANopenSlave_ServoErrSaferErr                          
0     0000b550  _OD_CANopenSlave_ServoErrPositionOver                      
0     0000b558  _OD_CANopenSlave_ServoErrDITrigErr                         
0     0000b560  _OD_CANopenSlave_ServoErrChargerComErr                     
0     0000b568  _OD_CANopenSlave_ServoErrMotoLineOff                       
0     0000b570  _OD_CANopenSlave_StoreParameters                           
0     0000b580  _OD_CANopenSlave_IdentifyObject                            
0     0000b58a  _OD_CANopenSlave_PredefineErrorField                       
0     0000b59c  _OD_CANopenSlave_ManufacturerSoftwareVersion               
0     0000b5c0  _OD_CANopenSlave_ManufacturerDeviceName                    
0     0000b5d4  _OD_CANopenSlave_ManufacturerHardwareVersion               
0     0000b600  _OD_CANopenSlave_RXPDOParameter                            
0     0000b640  _OD_CANopenSlave_TXPDOParameter                            
0     0000b680  _OD_CANopenSlave_RXPDOMapping                              
0     0000b700  _OD_CANopenSlave_TXPDOMapping                              
0     0000b780  _CANopenSlave_ODList                                       
0     0000c6fa  _SSIEncoderAngleValue                                      
0     0000c6fc  _SSICalculateAngleValue                                    
0     0000c707  _SDFormatCommondDone                                       
0     0000c708  _res                                                       
0     0000c709  _br                                                        
0     0000c70a  _SDSPIBusy                                                 
0     0000c70b  _HistoryErrorReadLine                                      
0     0000c70c  _h                                                         
0     0000c740  _SDReadBuffer                                              
0     0000ccc0  _fs                                                        
0     0000ceda  _HistoryErrorReadCommand                                   
0     0000cedb  _HistoryErrorWriteCommand                                  
0     0000cedd  _ErrorFileTextName                                         
0     0000ceec  _HistoryErrorFileTextTemp                                  
0     0000cf00  _LoggerSampleDoing                                         
0     0000cf01  _LoggerSampleDone                                          
0     0000cf03  _DataLoggerMaxLine                                         
0     0000cf04  _DataLoggerAfterLine                                       
0     0000cf05  _LoggerStartRecord                                         
0     0000cf07  _LoggerReadFileNo                                          
0     0000cf09  _LoggerTimeTransTemp                                       
0     0000cf0b  _LoggerReadFileUpload                                      
0     0000cf0c  _LoggerReadFileSend                                        
0     0000cf0d  _DataLoggerBeforeTime                                      
0     0000cf0e  _DataLoggerBeforeLine                                      
0     0000cf0f  _DataLoggerFileCount                                       
0     0000cf10  _DataLoggerAfterTime                                       
0     0000cf20  _LoggerReadFileSendDataPacketIndex                         
0     0000cf36  _stcLoggerTriggerTime                                      
0     0000cf40  _DataLoggerFileTextNameTemp                                
0     0000cf4a  _DataLoggerFileTextTemp                                    
0     0000cf5e  _DataLoggerFileTextName                                    
0     0000cf80  _DataLoggerDataTempName                                    
0     0000cfc0  _DataLoggerDataTempValue                                   
0     0000d000  _DataLoggerFileText10Line                                  
0     0000d5dc  _CpuTimer1                                                 
0     0000d5e4  _CpuTimer2                                                 
0     0000d5ec  _CpuTimer0                                                 
0     0000d5f4  ___TI_enable_exit_profile_output                           
0     0000d5f6  ___TI_cleanup_ptr                                          
0     0000d5f8  ___TI_dtors_ptr                                            
0     0000d5fe  __lock                                                     
0     0000d602  _CANopenSlave_HeartBeat_TimeOut                            
0     0000d603  _CANopenSlaveStatus                                        
0     0000d604  _CANopenSlaveNMTMessageFlag                                
0     0000d605  _CANopenSlaveNMTMessageCommand                             
0     0000d608  _CANopenSlave_NodeGuarding_TimeOut                         
0     0000d609  _CANopenSlave_BootUp_Delay                                 
0     0000d60a  _CANopenSlave_HeartBeat_Delay                              
0     0000d60e  _CANopenSlaveErrorControl                                  
0     0000d617  _CANopenSlaveTXPDOEventTimer                               
0     0000d621  _CANopenSlaveTXPDOInhibitTimer                             
0     0000d62b  _CANopenSlaveTXPDOEnable                                   
0     0000d635  _CANopenSlaveTXPDOSyncTimer                                
0     0000d640  _CANopenSlave_SDOserverVar                                 
0     0000d6c0  _ErrorCodesTable                                           
0     0000d740  _CANopenSlave_Mapping_RXPDO                                
0     0000d840  _CANopenSlave_Mapping_TXPDO                                
0     0000d940  _CANopenSlaveTXMessage                                     
0     0000dac0  _CANopenSlaveRXMessage                                     
0     0000dc34  _StatemachineRunToEmergencyFlag                            
0     0000dc3c  _stcStateMachine                                           
0     0000dc40  _ModBusTCPHandleDataDone                                   
0     0000dc42  _SCIAReceiveDataHeartBeat                                  
0     0000dc44  _ModBusTCPSendDataBusy                                     
0     0000dc46  _ModBusTCPSendWaitMode                                     
0     0000dc4a  _InternalModBusTCPConfigData                               
0     0000dc80  _ModBusTCPDataHold                                         
0     0000dd00  _ModBusTCPDataInput                                        
0     0000dd80  _ModBusTCPCommunicationData                                
0     0000e1f6  __unlock                                                   
0     0000e1f8  _SD_Stat                                                   
0     0000e1f9  _errno                                                     
0     0000e21d  _MotorParameterReceive                                     
0     0000e21e  _MotorParameterDataPacketReceiveByteIndex                  
0     0000e21f  _MotorParameterUpload                                      
0     0000e220  _MotorParameterSend                                        
0     0000e221  _MotorParameterDataPacketReceiveNo                         
0     0000e222  _MotorParameterSendDataPacketIndex                         
0     0000e223  _MotorParameterHandle                                      
0     0000e224  _MotorParameterDownload                                    
0     0000e280  _stcInverterExternalInformation                            
0     0000e300  _stcInverterInternalInformation                            
0     0000e380  _MotorParameterReceiveDataBuffer                           
0     0000e794  _RxPDOReceiveCheckFlag                                     
0     0000e795  _CANopenMasterStatus                                       
0     0000e796  _CANopenMasterCommunicationParameterInitDone               
0     0000e79e  _RxPDOReturnCount                                          
0     0000e7c0  _CANopenMasterSDODataType                                  
0     0000e900  _CANopenMasterRXMessage                                    
0     0000ea80  _CANopenMasterTXMessage                                    
0     0000ec00  _ModBus_Hold_RW_SubIndex                                   
0     0000ec01  _ModBus_Hold_RW_Index                                      
0     0000ec02  _ModBusStatus                                              
0     0000ec03  _ModBus_Input_Real_Time_Error_Page_Current                 
0     0000ec04  _ModBus_Hold_CANopenSlaveBaudrateOption                    
0     0000ec05  _ModBus_Hold_RW_Flag                                       
0     0000ec06  _ModBus_Input_Real_Time_Error_Page_Sum                     
0     0000ec07  _ModBus_Hold_Command_Set_Motor_Parameter                   
0     0000ec08  _ModBus_Hold_Command_ProximityCalibration                  
0     0000ec09  _ModBus_Hold_Command_Test_Button_Display                   
0     0000ec0a  _ModBus_Hold_Command_CANopenSlaveBaudrateSet               
0     0000ec0b  _ModBus_Hold_Command_ProximityReset                        
0     0000ec0c  _ModBus_Input_Code_Version_External                        
0     0000ec0d  _ModBus_Hold_Command_System_Parameter_Init                 
0     0000ec0e  _ModBus_Hold_Command_Reboot_System                         
0     0000ec0f  _ModBus_Input_Code_Version                                 
0     0000ec10  _ModBus_Hold_Command_SDFormat                              
0     0000ec11  _ModBus_Hold_Command_Jog_Stop                              
0     0000ec12  _ModBus_Hold_Command_Calibrate_0                           
0     0000ec13  _ModBus_Hold_Command_Jog_Backward                          
0     0000ec14  _ModBus_Hold_Command_Jog_Speed_Level                       
0     0000ec15  _ModBus_Hold_Command_Ultracapacitor_Test                   
0     0000ec16  _ModBus_Hold_Command_Ultracapacitor_Stop_Charge            
0     0000ec17  _ModBus_Hold_Command_Calibrate_LSA                         
0     0000ec18  _ModBus_Hold_Command_ByPass_LSB                            
0     0000ec19  _ModBus_Hold_Relay                                         
0     0000ec1a  _ModBus_Hold_Command_Ultracapacitor_Discharge              
0     0000ec1b  _ModBus_Hold_Heartbeat                                     
0     0000ec1c  _ModBus_Hold_MainPowerOff                                  
0     0000ec1d  _ModBus_Hold_Command_Reset                                 
0     0000ec1e  _ModBus_Hold_Command_Jog_Forward                           
0     0000ec1f  _ModBus_Input_History_Error_Page_Sum                       
0     0000ec20  _ModBus_Input_History_Error_Page_Current                   
0     0000ec21  _ModBus_Hold_History_Error_Page_Up_Last                    
0     0000ec22  _ModBus_Hold_History_Error_Page_Down_Last                  
0     0000ec23  _ModBus_Hold_Real_Time_Error_Page_Up_Last                  
0     0000ec24  _ModBus_Hold_Real_Time_Error_Page_Down_Last                
0     0000ec25  _ModBus_Input_State_Machine_Mode_Emergency_Run             
0     0000ec26  _ModBus_Input_State_Machine_Mode_Emergency_Stop            
0     0000ec27  _ModBus_Input_State_Machine_Mode_Normal_Stop               
0     0000ec28  _ModBus_Input_State_Machine_Mode_Normal_Operation          
0     0000ec29  _ModBusNodeID                                              
0     0000ec2a  _ModBusConnectionFlag                                      
0     0000ec2b  _ModBusEnable                                              
0     0000ec2e  _ModBusError                                               
0     0000ec2f  _ModBusTimeOutDelay                                        
0     0000ec31  _ModBus_Hold_History_Error_Page_Up                         
0     0000ec32  _ModBus_Hold_History_Error_Page_First                      
0     0000ec33  _ModBus_Hold_History_Error_Page_Down                       
0     0000ec34  _ModBus_Input_Heartbeat                                    
0     0000ec35  _ModBus_Hold_Real_Time_Error_Page_First                    
0     0000ec36  _ModBus_Hold_Command_Set_Drive_Time                        
0     0000ec37  _ModBus_Hold_Real_Time_Error_Page_Down                     
0     0000ec38  _ModBus_Hold_Real_Time_Error_Page_Up                       
0     0000ec39  _ModBus_Input_State_Machine_Mode_Reset                     
0     0000ec3a  _ModBus_Input_Calibration_Position_Done                    
0     0000ec3b  _ModBus_Input_State_Machine_Mode_Ultracapacitor_Test       
0     0000ec3c  _ModBus_Input_State_Machine_Mode_Init                      
0     0000ec3d  _ModBus_Input_MainPowerOff                                 
0     0000ec3e  _ModBus_Hold_History_Error_Reset                           
0     0000ec3f  _ModBus_Input_Calibration_Proximity_Switch_Done            
0     0000ec40  _ModBus_Input_Calibration_Motor_Motion_Parameter_Done      
0     0000ec41  _ModBus_Input_State_Machine_Mode_Manual                    
0     0000ec42  _ModBus_Input_Motor_Torque                                 
0     0000ec44  _ModBus_Input_Motor_Current                                
0     0000ec46  _ModBus_Input_Ultracapacitor_Voltage                       
0     0000ec48  _ModBus_Input_DC_Bus_Voltage                               
0     0000ec4a  _ModBus_Input_Drive_Temperature                            
0     0000ec4c  _ModBus_Input_Actual_Speed                                 
0     0000ec4e  _ModBus_Input_Actual_Position                              
0     0000ec50  _ModBus_Input_Read_Data                                    
0     0000ec52  _ModBus_Input_Gear_Ratio                                   
0     0000ec54  _ModBus_Hold_Calibrate_Time_Value                          
0     0000ec56  _ModBus_Hold_Write_Data                                    
0     0000ec58  _ModBus_Input_Motor_Temperature                            
0     0000ec5a  _ModBus_Input_ActualTime                                   
0     0000ec5c  _ModBus_Input_AI                                           
0     0000ec60  _ModBus_Input_PT100                                        
0     0000ec66  _ModBus_Hold_DO                                            
0     0000ec6f  _ModBus_Input_Trigger_Count_Real_Time_Error                
0     0000ec80  _ModBus_Input_Number_History_Error                         
0     0000ec8a  _ModBus_Input_Time_Low_Real_Time_Error                     
0     0000ec94  _ModBus_Input_ID_Real_Time_Error                           
0     0000ec9e  _ModBus_Input_Time_High_Real_Time_Error                    
0     0000eca8  _ModBus_Input_Reset_Time_High_History_Error                
0     0000ecb2  _ModBus_Input_Reset_Time_Low_History_Error                 
0     0000ecc0  _ModBus_Input_Trigger_Time_Low_History_Error               
0     0000ecca  _ModBus_Input_ID_History_Error                             
0     0000ecd4  _ModBus_Input_Trigger_Time_High_History_Error              
0     0000ecde  _ModBus_Input_Number_Real_Time_Error                       
0     0000ece8  _ModBusConfig                                              
0     0000ed00  _ModBus_Input_DI                                           
0     0000ed40  _ModBusDataInput                                           
0     0000ee40  _ModBusDataHold                                            
0     0000ef08  _data_manipulation                                         
0     0000ef09  _high_capacity                                             
0     0000ef0b  _response                                                  
0     0000ef0c  _crc_enabled                                               
0     0000ef0d  _card_status                                               
0     0000ef0f  _ocr_contents                                              
0     0000ef14  _csd_contents                                              
0     0000ef24  _cid_contents                                              
0     0000ef46  _MOTION_CONTROL_SPEED_COMPENSATION                         
0     0000ef4a  _STATE_MACHINE_POSITION_KP1                                
0     0000ef4c  _STATE_MACHINE_POSITION_KP2                                
0     0000ef4e  _MotionControlPositionModePlanPosition                     
0     0000ef50  _MOTION_CONTROL_SPEED_KI_VALUE                             
0     0000ef52  _MOTION_CONTROL_SPEED_KD_VALUE                             
0     0000ef54  _MOTION_CONTROL_SPEED_PLAN_COEFFICIENT                     
0     0000ef56  _MOTION_CONTROL_SPEED_KP_VALUE                             
0     0000ef80  _stcMotionControlInternalVariable                          
0     0000f200  _ParameterWRCommand                                        
0     0000f716  _CANopenMasterErrorRXMBoxIndex                             
0     0000f717  _CANopenMasterRXMBoxIndex                                  
0     0000f718  _ProgramInitSuccess                                        
0     0000f719  _CANopenMasterRXMBoxErrorID                                
0     0000f71a  _CANopenSlaveRXMBoxIndex                                   
0     0000f71b  _CANopenSlaveRXMBoxErrorID                                 
0     0000f71c  _CANopenSlaveErrorRXMBoxIndex                              
0     0000f71d  _PCToolToReboot                                            
0     0000f71e  _SDReInstallToReboot                                       
0     0000f71f  _SDDataWRType                                              
0     0000f720  _SDDataWRBusy                                              
0     0000f721  _ProgramStartSuccess                                       
0     0000f722  _SDDataInitSuccess                                         
0     0000f723  _SDHandleFileType                                          
0     0000f724  _SDWRRuning                                                
0     0000f780  _AWS                                                       
0     0000f800  _InternalErrorSaveToDataLog                                
0     0000f802  _ErrorDataLoggerSampleData                                 
0     0000f840  _DataLoggerInitSet                                         
0     0000f8c1  _OD_CANopenSlave_Controlword_Value                         
0     0000f900  _SystemVariablDI                                           
0     0000f9ae  _RotaryDataCalculate                                       
0     0000f9b6  _RotaryData                                                
0     0000f9c0  _SDCardInfo                                                
0     0000ff00  _InitFlash                                                 
0     0000ff00  _RamfuncsRunStart                                          
0     0000ff1b  _DSP28x_usDelay                                            
0     00200000  _DataLogger                                                
0     00208340  _DataLoggerUploadList                                      
0     00231b80  _HistoryErrorWriteChar                                     
0     00233ac0  _FaultCodePropertyContent                                  
0     00233bc0  _FaultCodeSortHistory                                      
0     00233ec0  _FaultCodePropertySetInit                                  
0     00235340  _MotorParameterList                                        
0     00300000  code_start                                                 
0     00300002  .text                                                      
0     00300002  _CANOpenMasterStatusInit                                   
0     00300002  ___text__                                                  
0     0030001d  _CANOpenMasterNMTControl                                   
0     00300190  _CANOpenMasterReceivePDOData                               
0     003004a1  _CANOpenMasterSendPDOData                                  
0     00300538  _CANOpenMasterMessageInit                                  
0     003005a9  _CANOpenMasterSetup                                        
0     003006ca  _CANOpenMasterODInit                                       
0     00300f41  _CANOpenMasterSDOParameterInit                             
0     003014ce  _CANOpenMasterCalibratePosition                            
0     003015ea  _CANOpenMasterSDOWRData                                    
0     00303231  _CANOpenMasterSDOWR                                        
0     003032b7  _CANOpenMasterSDOCommand                                   
0     00303309  _CANOpenMasterSDOConfirm                                   
0     0030334b  _CANOpenMaster_TXMessageToBuffers                          
0     003033a5  _FaultCodeInternalInit                                     
0     00303744  _FaultCodeInternalRun                                      
0     003037f2  _FaultCodeErrorConfig                                      
0     00303824  _FaultCodeErrorConfigSHEODToInternal                       
0     0030385c  _FaultCodeErrorConfigSHEInternalToOD                       
0     00303885  _FaultCodeErrorConfigEnd                                   
0     00303913  _FaultCodeErrorConfigSHEODToInternalAll                    
0     00303b26  _FaultCodeErrorConfigSHEInternalToODAll                    
0     00303d39  _FaultCodePropertyWRRun                                    
0     00303d69  _FaultCodeGetStatus                                        
0     00303d6d  _FaultCodeGetSafetyChainStatus                             
0     00303d71  _FaultCodeSet                                              
0     00303f5a  _FaultCodeOncelReset                                       
0     00303fb3  _FaultCodeManualReset                                      
0     00303fe2  _FaultCodeSortHistoryReset                                 
0     00303ff6  _FaultCodeGetTriggerStatus                                 
0     00304002  _FaultCodeGetCode                                          
0     00304013  _FaultCodePropertyWRSD                                     
0     00304bb7  _FaultCodePropertyWRSDReset                                
0     00304bbb  _FaultCodeTrigger                                          
0     00304c0f  _FaultCodeAutomaticReset                                   
0     00304e22  _FaultCodeInverterResetCommandReset                        
0     00304e3d  _FaultCodeSortRealTimeTrigger                              
0     00304eaa  _FaultCodeSortHistoryTrigger                               
0     00304f07  _FaultCodeSortRealTimeReset                                
0     00304f87  _FaultCodeGetHistoryResetTime                              
0     00304fb5  _FaultCodeInternalErrorTrigger                             
0     00305541  _clust2sect                                                
0     0030555b  _get_fat                                                   
0     003055f6  _put_fat                                                   
0     00305f02  _f_mount                                                   
0     00305f26  _f_open                                                    
0     00306069  _f_read                                                    
0     00306180  _f_write                                                   
0     003062c4  _f_sync                                                    
0     00306374  _f_close                                                   
0     00306381  _f_simple_close                                            
0     00306389  _f_lseek                                                   
0     003064b5  _f_opendir                                                 
0     00306519  _f_readdir                                                 
0     00306555  _f_stat                                                    
0     0030657f  _f_getfree                                                 
0     00306637  _f_truncate                                                
0     003066aa  _f_unlink                                                  
0     00306736  _f_linkInit                                                
0     003067ac  _f_mkdir                                                   
0     003068d9  _f_chmod                                                   
0     00306918  _f_utime                                                   
0     00306962  _f_rename                                                  
0     00306a3a  _f_mkfs                                                    
0     00306e8a  _f_gets                                                    
0     00306ec0  _f_putc                                                    
0     00306edc  _f_puts                                                    
0     00306ef5  _f_printf                                                  
0     00307015  _InverterControlInit                                       
0     0030703e  _InverterMotionCommandWriteToInverter                      
0     003070f3  _InverterInformationWriteToInverterRun                     
0     00307285  _InverterInformationWriteToControlICRun                    
0     003073ab  _InverterDateToSeconds                                     
0     003074e3  _InverterSecondsToDate                                     
0     0030760e  _InverterControlDisCharge                                  
0     0030764c  _InverterControlCharge                                     
0     003076ef  _InverterControlCalculateCapacitanceValue                  
0     0030785c  _InverterControlCalculateCapacitanceReset                  
0     0030788d  _InverterControlHubSpeedCalculate                          
0     0030793a  _InverterControlEmergency                                  
0     00307948  _InverterControlMotorParameterSelect                       
0     003079b6  _InverterControlMotorParameterUpload                       
0     00307da9  _InverterControlMotorParameterDownload                     
0     003080be  _InverterControlMotorParameterUploadTimeOut                
0     003080f8  _InverterControlMotorParameterDownloadTimeOut              
0     0030816b  _InverterControlInverterTimeCalibrate                      
0     00308191  _InverterControlInverterTimeCalibrateTimeOut               
0     003081ad  _InverterCheckError                                        
0     00308228  _InverterNixieTubeDisplay                                  
0     0030831f  _InverterMotorParameterCharToInt                           
0     003083a8  _CANOpenSlaveStatusInit                                    
0     003083ae  _CANOpenSlaveNMTControl                                    
0     00308843  _CANOpenSlaveReceiveData                                   
0     003089fa  _CANOpenSlaveSendData                                      
0     00308c73  _CANOpenSlaveBaudrateSet                                   
0     00308c7e  _CANOpenSlaveDataInit                                      
0     00308cae  _CANOpenSlaveInit                                          
0     00308cb9  _CANOpenSlaveComminit                                      
0     003091fa  _CANOpenSlaveSetup                                         
0     0030930c  _CANOpenSlaveFindEntryInOD                                 
0     00309379  _CANOpenSlaveErrorReport                                   
0     00309391  _CANOpenSlaveCommunicationParameterChange                  
0     00309399  _CANOpenSlave_ErrorDataFrameResultInterruptStop            
0     003093c3  _CANOpenSlave_TXMessageToBuffers                           
0     0030941d  _ModBusTCPSlaveConfig                                      
0     0030944c  _ModBusTCPSlaveInit                                        
0     0030949b  _ModbusTCPSlaveReceive                                     
0     0030970d  _ModbusTCPSlaveSend                                        
0     0030973d  _ModBusTCPSlaveCommunicationDataHandle                     
0     00309ea1  _CRC16                                                     
0     00309ec7  _ModbusTCPSlaveInvalidDataAbandon                          
0     00309f19  _SCIA_Init                                                 
0     00309f32  _SCIB_Init                                                 
0     00309f4b  _AWSInit                                                   
0     0030a012  _AWSBackground                                             
0     0030a251  _AWSSystemReboot                                           
0     0030a256  _AWSVariableInit                                           
0     0030a26b  _AWSVariableInputRun                                       
0     0030a4c0  _AWSVariableOutputRun                                      
0     0030a510  _AWSLibFunctionRun                                         
0     0030a765  _AWSInverterCommunicationRun                               
0     0030a771  _AWSMasterCommunicationRun                                 
0     0030a785  _AWSDataInteractionInput                                   
0     0030a791  _AWSDataInteractionOutput                                  
0     0030a79f  _AWSSDWRPriorityJudge                                      
0     0030a843  _AWSSDWRTimeOut                                            
0     0030a893  _AWSRTCReadTimeTransformation                              
0     0030a8f1  _AWSRTCReadMPUTransformation                               
0     0030a906  _ParameterWRInit                                           
0     0030ab9c  _ParameterWRRun                                            
0     0030ac42  _ParameterWRSDConfigInteger                                
0     0030accd  _ParameterWRSDConfigLong                                   
0     0030ad59  _ParameterWRSDConfigFloat                                  
0     0030adec  _ParameterWRSDConfigEnd                                    
0     0030adf1  _ParameterWRSD                                             
0     0030b267  _ParameterWRSDReset                                        
0     0030b26b  _FileSDInit                                                
0     0030b282  _FileSDWrite                                               
0     0030b41b  _FileSDRead                                                
0     0030b60a  _FileSDFormat                                              
0     0030b619  _FileStringByteLength                                      
0     0030b627  _FileFloatToChar                                           
0     0030b73c  _FileIntToChar                                             
0     0030b7d6  _FileLongToChar                                            
0     0030b847  _FileCharToFloat                                           
0     0030b8c5  _FileCharToInt                                             
0     0030b8f6  _FileCharToLong                                            
0     0030b935  _FileParameterCharacterClassify                            
0     0030b9a6  _HistoryErrorCharToValue                                   
0     0030bbb1  _LoggerWriteInit                                           
0     0030bc6e  _LoggerWriteRecord                                         
0     0030bfb6  _LoggerWriteSD                                             
0     0030c372  _LoggerReadSD                                              
0     0030c3b6  _LoggerReadTimeout                                         
0     0030c481  _StateMachineInit                                          
0     0030c48d  _StateMachineRun                                           
0     0030c4ae  _StateMachine_Status_Switch                                
0     0030c75c  _StateMachine_Status_Init                                  
0     0030c78e  _StateMachine_Status_Stop                                  
0     0030c915  _StateMachine_Status_Velocity                              
0     0030c95f  _StateMachine_Status_Position                              
0     0030c9b6  _StateMachine_Status_EmergencyRun                          
0     0030cb60  _MotionControlInit                                         
0     0030cb6e  _MotionControlVariableFilter                               
0     0030cba9  _MotionControlSpeedPlan                                    
0     0030cc27  _MotionControlSpeedPID                                     
0     0030cd8a  _MotionControlRun                                          
0     0030d0a2  _TaskCycle2MS                                              
0     0030d10f  _TaskCycle10MS                                             
0     0030d156  _TaskCycle100MS                                            
0     0030d1a0  _SCIA_Receive                                              
0     0030d29c  _SCIB_Receive                                              
0     0030d2d1  _ADC_Collect                                               
0     0030d2e2  _CANA_Receive_With_Master                                  
0     0030d41b  _CANB_Receive_With_Inverter                                
0     0030d4fc  _EPWM3_INT_CLK                                             
0     0030d5da  _SystemVariableInit                                        
0     0030d5e2  _SystemVariableRun10MS                                     
0     0030d98c  _SystemVariableRun100MS                                    
0     0030da89  _SystemVariableRun1S                                       
0     0030da8d  _SystemVariableIOTriggerDelay                              
0     0030daae  _ModBusInit                                                
0     0030daee  _ModBusRun                                                 
0     0030dafb  _ModBusStatusCheck                                         
0     0030db11  _ModBusErrorInformationDisplay                             
0     0030dd11  _ModBusChannelDataAssignment                               
0     0030deab  _ModBusChannelDataInit                                     
0     0030df53  _ModBusChannelDataFistInDataHandle                         
0     0030df76  _IOInit                                                    
0     0030df7b  _IOInputRun                                                
0     0030e061  _IOOutputRun                                               
0     0030e0fb  _IOControlICGPIOConfig                                     
0     0030e213  _ADCControlICConfig                                        
0     0030e258  _ADCToPT100Temperature                                     
0     0030e2f9  _ADCToAIVoltage                                            
0     0030e38d  _ADCToAICurrent                                            
0     0030e42a  _MPU_Init                                                  
0     0030e472  _MPU_Run                                                   
0     0030e492  _MPU_Get_Temperature                                       
0     0030e4c2  _MPU_Get_Gyroscope                                         
0     0030e4e8  _MPU_Get_Accelerometer                                     
0     0030e50e  _MPU_Write_Len                                             
0     0030e53f  _MPU_Read_Len                                              
0     0030e57a  _MPU_Write_Byte                                            
0     0030e59f  _MPU_Read_Byte                                             
0     0030e5be  _MPU_Set_Gyro_Fsr                                          
0     0030e5c8  _MPU_Set_Accel_Fsr                                         
0     0030e5d2  _MPU_Set_LPF                                               
0     0030e5f6  _MPU_Set_Rate                                              
0     0030e615  _MPU_Get_Angle                                             
0     0030e86a  _ManualInit                                                
0     0030e86b  _ManualCalibrationPosition                                 
0     0030e8a2  _ManualPositionStatusCheck                                 
0     0030e8cf  _ManualCalibrationProximity0                               
0     0030e975  _ManualCalibrationProximity0Reset                          
0     0030e981  _ManualProximity0StatusCheck                               
0     0030e9cc  _ManualCalibrationProximity1                               
0     0030ea72  _ManualCalibrationProximity1Reset                          
0     0030ea7e  _ManualProximity1StatusCheck                               
0     0030ead3  _ManualCalibrationProximity2                               
0     0030eb79  _ManualCalibrationProximity2Reset                          
0     0030eb85  _ManualProximity2StatusCheck                               
0     0030ebda  _ManualJogMove                                             
0     0030ebf8  _INT13_ISR                                                 
0     0030ec02  _INT14_ISR                                                 
0     0030ec0c  _DATALOG_ISR                                               
0     0030ec16  _RTOSINT_ISR                                               
0     0030ec20  _EMUINT_ISR                                                
0     0030ec2a  _NMI_ISR                                                   
0     0030ec34  _ILLEGAL_ISR                                               
0     0030ec45  _USER1_ISR                                                 
0     0030ec4f  _USER2_ISR                                                 
0     0030ec59  _USER3_ISR                                                 
0     0030ec63  _USER4_ISR                                                 
0     0030ec6d  _USER5_ISR                                                 
0     0030ec77  _USER6_ISR                                                 
0     0030ec81  _USER7_ISR                                                 
0     0030ec8b  _USER8_ISR                                                 
0     0030ec95  _USER9_ISR                                                 
0     0030ec9f  _USER10_ISR                                                
0     0030eca9  _USER11_ISR                                                
0     0030ecb3  _USER12_ISR                                                
0     0030ecbd  _SEQ1INT_ISR                                               
0     0030ecc7  _SEQ2INT_ISR                                               
0     0030ecd1  _XINT1_ISR                                                 
0     0030ecdb  _XINT2_ISR                                                 
0     0030ece5  _ADCINT_ISR                                                
0     0030ecef  _TINT0_ISR                                                 
0     0030ecf9  _WAKEINT_ISR                                               
0     0030ed03  _EPWM1_TZINT_ISR                                           
0     0030ed0d  _EPWM2_TZINT_ISR                                           
0     0030ed17  _EPWM3_TZINT_ISR                                           
0     0030ed21  _EPWM4_TZINT_ISR                                           
0     0030ed2b  _EPWM5_TZINT_ISR                                           
0     0030ed35  _EPWM6_TZINT_ISR                                           
0     0030ed3f  _EPWM1_INT_ISR                                             
0     0030ed49  _EPWM2_INT_ISR                                             
0     0030ed53  _EPWM3_INT_ISR                                             
0     0030ed5d  _EPWM4_INT_ISR                                             
0     0030ed67  _EPWM5_INT_ISR                                             
0     0030ed71  _EPWM6_INT_ISR                                             
0     0030ed7b  _ECAP1_INT_ISR                                             
0     0030ed85  _ECAP2_INT_ISR                                             
0     0030ed8f  _ECAP3_INT_ISR                                             
0     0030ed99  _ECAP4_INT_ISR                                             
0     0030eda3  _ECAP5_INT_ISR                                             
0     0030edad  _ECAP6_INT_ISR                                             
0     0030edb7  _EQEP1_INT_ISR                                             
0     0030edc1  _EQEP2_INT_ISR                                             
0     0030edcb  _SPIRXINTA_ISR                                             
0     0030edd5  _SPITXINTA_ISR                                             
0     0030eddf  _MRINTB_ISR                                                
0     0030ede9  _MXINTB_ISR                                                
0     0030edf3  _MRINTA_ISR                                                
0     0030edfd  _MXINTA_ISR                                                
0     0030ee07  _DINTCH1_ISR                                               
0     0030ee11  _DINTCH2_ISR                                               
0     0030ee1b  _DINTCH3_ISR                                               
0     0030ee25  _DINTCH4_ISR                                               
0     0030ee2f  _DINTCH5_ISR                                               
0     0030ee39  _DINTCH6_ISR                                               
0     0030ee43  _I2CINT1A_ISR                                              
0     0030ee4d  _I2CINT2A_ISR                                              
0     0030ee57  _SCIRXINTC_ISR                                             
0     0030ee61  _SCITXINTC_ISR                                             
0     0030ee6b  _SCIRXINTA_ISR                                             
0     0030ee75  _SCITXINTA_ISR                                             
0     0030ee7f  _SCIRXINTB_ISR                                             
0     0030ee89  _SCITXINTB_ISR                                             
0     0030ee93  _ECAN0INTA_ISR                                             
0     0030ee9d  _ECAN1INTA_ISR                                             
0     0030eea7  _ECAN0INTB_ISR                                             
0     0030eeb1  _ECAN1INTB_ISR                                             
0     0030eebb  _XINT3_ISR                                                 
0     0030eec5  _XINT4_ISR                                                 
0     0030eecf  _XINT5_ISR                                                 
0     0030eed9  _XINT6_ISR                                                 
0     0030eee3  _XINT7_ISR                                                 
0     0030eeed  _LVF_ISR                                                   
0     0030eef7  _LUF_ISR                                                   
0     0030ef01  _EMPTY_ISR                                                 
0     0030ef0e  _PIE_RESERVED                                              
0     0030ef18  _rsvd_ISR                                                  
0     0030ef22  _HistoryErrorInit                                          
0     0030ef29  _HistoryErrorRead                                          
0     0030efb6  _HistoryErrorWrite                                         
0     0030f22d  _RTCInit                                                   
0     0030f26a  _RTCRead                                                   
0     0030f2a8  _RTCWrite                                                  
0     0030f2d3  _RTCReadDateTime                                           
0     0030f348  _RTCWriteDateTime                                          
0     0030f3cc  _spi_initialization                                        
0     0030f3d9  _sd_card_insertion                                         
0     0030f3f6  _sd_initialization                                         
0     0030f460  _sd_version1_initialization                                
0     0030f4ca  _sd_version2_initialization                                
0     0030f551  _InitECan                                                  
0     0030f557  _InitECana                                                 
0     0030f608  _InitECanb                                                 
0     0030f688  _InitECanGpio                                              
0     0030f68d  _InitECanaGpio                                             
0     0030f69d  _InitECanbGpio                                             
0     0030f6ad  _disk_initialize                                           
0     0030f6d0  _disk_status                                               
0     0030f6dd  _disk_read                                                 
0     0030f6f8  _disk_write                                                
0     0030f710  _disk_ioctl                                                
0     0030f7d6  _get_fattime                                               
0     0030f7ff  _ErrorInit                                                 
0     0030f894  _ErrorRun                                                  
0     0030f948  _FaultCodeInit                                             
0     0030f94b  _InitSysCtrl                                               
0     0030f954  _ServiceDog                                                
0     0030f95e  _DisableDog                                                
0     0030f966  _InitPll                                                   
0     0030f9b7  _InitPeripheralClocks                                      
0     0030fa3d  _CsmUnlock                                                 
0     0030fa7e  _I2C_Init                                                  
0     0030fa9a  _I2C_Start                                                 
0     0030fab1  _I2C_Stop                                                  
0     0030fac6  _I2C_Wait_Ack                                              
0     0030faea  _I2C_Ack                                                   
0     0030fb03  _I2C_NAck                                                  
0     0030fb1c  _I2CByteWrite                                              
0     0030fb4c  _I2CByteRead                                               
0     0030fb79  _I2CCommunicationDelay                                     
0     0030fb87  LL$$DIV                                                    
0     0030fbc3  LL$$MOD                                                    
0     0030fbfd  ULL$$DIV                                                   
0     0030fc2c  ULL$$MOD                                                   
0     0030fc8e  _sd_write_block                                            
0     0030fcc5  _sd_write_multiple_block                                   
0     0030fd13  _sd_write_data                                             
0     0030fd87  _InitXintf                                                 
0     0030fde0  _InitXintf32Gpio                                           
0     0030fe27  _InitXintf16Gpio                                           
0     0030fe7d  _asin                                                      
0     0030fe7d  _asinf                                                     
0     0030ff6e  _sd_read_block                                             
0     0030ff9e  _sd_read_multiple_block                                    
0     0030ffeb  _sd_data_response                                          
0     00310044  _atan2                                                     
0     00310044  _atan2f                                                    
0     00310118  _sd_read_register                                          
0     00310147  _sd_ocr_response                                           
0     00310164  _sd_cid_csd_response                                       
0     003101ae  _sd_send_status                                            
0     003101e5  _atan                                                      
0     003101e5  _atanf                                                     
0     003102af  _spi_xmit_byte                                             
0     003102bd  _spi_xmit_command                                          
0     00310301  _sd_crc7                                                   
0     00310330  _sd_command_response                                       
0     0031034d  _sd_error                                                  
0     00310356  FD$$ADD                                                    
0     003103f2  _SSIInit                                                   
0     00310430  _SSIRun                                                    
0     00310458  _SSICalibrationHardware                                    
0     0031045d  _SSICalibrationSoftware                                    
0     00310476  _SSIDirection                                              
0     00310485  __system_post_cinit                                        
0     00310486  _sqrtl                                                     
0     00310518  FD$$DIV                                                    
0     003105a3  FS$$DIV                                                    
0     0031062b  FD$$MPY                                                    
0     003106ae  _InitCpuTimers                                             
0     003106ef  _ConfigCpuTimer                                            
0     00310729  _SD_Init                                                   
0     0031073f  _SD_ReadBlock                                              
0     00310754  _SD_ReadMultiBlocks                                        
0     0031076a  _SD_WriteBlock                                             
0     0031077f  _SD_WriteMultiBlocks                                       
0     00310795  _InitEPwm                                                  
0     00310796  _InitEPwmGpio                                              
0     00310799  _InitEPwm1Gpio                                             
0     003107ab  _InitEPwm2Gpio                                             
0     003107bd  _InitEPwm3Gpio                                             
0     003107ca  _InitEPwmSyncGpio                                          
0     003107de  _InitTzGpio                                                
0     003107f6  _ParameterInit                                             
0     00310849  _ParameterRun                                              
0     00310852  _InitAdc                                                   
0     003108aa  _c_int00                                                   
0     00310900  _main                                                      
0     00310913  _AWSTaskCycle2MS                                           
0     00310917  _AWSTaskCycle10MS                                          
0     00310923  _AWSTaskCycle100MS                                         
0     00310934  _AWSSpecialVariableAssignment                              
0     00310950  _InitSpi                                                   
0     00310951  _InitSpiGpio                                               
0     00310954  _InitSpiaGpio                                              
0     0031097c  _InitSci                                                   
0     0031097d  _InitSciGpio                                               
0     00310982  _InitSciaGpio                                              
0     00310997  _InitScibGpio                                              
0     003109a7  FD$$CMP                                                    
0     003109d1  L$$DIV                                                     
0     003109e0  L$$MOD                                                     
0     003109ee  UL$$DIV                                                    
0     003109f5  UL$$MOD                                                    
0     003109fb  C$$EXIT                                                    
0     003109fb  _abort                                                     
0     003109fd  _exit                                                      
0     00310a24  _InitPieCtrl                                               
0     00310a43  _EnableInterrupts                                          
0     00310a4c  _sqrt                                                      
0     00310a4c  _sqrtf                                                     
0     00310a72  _copy_in                                                   
0     00310a96  FD$$TOFS                                                   
0     00310ab9  I$$DIV                                                     
0     00310aca  I$$MOD                                                     
0     00310adb  _InitPieVectTable                                          
0     00310afb  LL$$CMP                                                    
0     00310b0d  ULL$$CMP                                                   
0     00310b19  _memcpy                                                    
0     00310b36  FS$$TOFD                                                   
0     00310b52  L$$TOFD                                                    
0     00310b6e  __args_main                                                
0     00310b87  LL$$AND                                                    
0     00310b8f  LL$$OR                                                     
0     00310b97  LL$$XOR                                                    
0     00310b9f  _MemCopy                                                   
0     00310bb2  FD$$SUB                                                    
0     00310bc0  _memset                                                    
0     00310bcc  U$$DIV                                                     
0     00310bd1  U$$MOD                                                     
0     00310bd7  _strcmp                                                    
0     00310be1  __register_unlock                                          
0     00310be5  __register_lock                                            
0     00310be9  __nop                                                      
0     00310bea  FD$$NEG                                                    
0     00310bf3  _strcat                                                    
0     00310c04  _strlen                                                    
0     00310c0c  _strcpy                                                    
0     00310c11  __system_pre_init                                          
0     00310c13  _RamfuncsLoadStart                                         
0     00310c13  ___etext__                                                 
0     00310c13  etext                                                      
0     00310c32  _RamfuncsLoadEnd                                           
0     00310c32  ___cinit__                                                 
0     00310c32  cinit                                                      
0     00312ea6  _PieVectTableInit                                          
0     00380080  _ADC_cal                                                   
1     00000400  __stack                                                    
1     00000800  __STACK_END                                                
1     00000880  _DevEmuRegs                                                
1     00000a80  _FlashRegs                                                 
1     00000ae0  _CsmRegs                                                   
1     00000b00  _AdcMirror                                                 
1     00000b20  _XintfRegs                                                 
1     00000c00  _CpuTimer0Regs                                             
1     00000c08  _CpuTimer1Regs                                             
1     00000c10  _CpuTimer2Regs                                             
1     00000ce0  _PieCtrlRegs                                               
1     00000d00  _PieVectTable                                              
1     00001000  _DmaRegs                                                   
1     00005000  _McbspaRegs                                                
1     00005040  _McbspbRegs                                                
1     00006000  _ECanaRegs                                                 
1     00006040  _ECanaLAMRegs                                              
1     00006080  _ECanaMOTSRegs                                             
1     000060c0  _ECanaMOTORegs                                             
1     00006100  _ECanaMboxes                                               
1     00006200  _ECanbRegs                                                 
1     00006240  _ECanbLAMRegs                                              
1     00006280  _ECanbMOTSRegs                                             
1     000062c0  _ECanbMOTORegs                                             
1     00006300  _ECanbMboxes                                               
1     00006800  _EPwm1Regs                                                 
1     00006840  _EPwm2Regs                                                 
1     00006880  _EPwm3Regs                                                 
1     000068c0  _EPwm4Regs                                                 
1     00006900  _EPwm5Regs                                                 
1     00006940  _EPwm6Regs                                                 
1     00006a00  _ECap1Regs                                                 
1     00006a20  _ECap2Regs                                                 
1     00006a40  _ECap3Regs                                                 
1     00006a60  _ECap4Regs                                                 
1     00006a80  _ECap5Regs                                                 
1     00006aa0  _ECap6Regs                                                 
1     00006b00  _EQep1Regs                                                 
1     00006b40  _EQep2Regs                                                 
1     00006f80  _GpioCtrlRegs                                              
1     00006fc0  _GpioDataRegs                                              
1     00006fe0  _GpioIntRegs                                               
1     00007010  _SysCtrlRegs                                               
1     00007040  _SpiaRegs                                                  
1     00007050  _SciaRegs                                                  
1     00007070  _XIntruptRegs                                              
1     00007100  _AdcRegs                                                   
1     00007750  _ScibRegs                                                  
1     00007770  _ScicRegs                                                  
1     00007900  _I2caRegs                                                  
1     0033fff8  _CsmPwl                                                    
abs   00000400  __STACK_SIZE                                               
abs   ffffffff  ___TI_pprof_out_hndl                                       
abs   ffffffff  ___TI_prof_data_size                                       
abs   ffffffff  ___TI_prof_data_start                                      
abs   ffffffff  ___binit__                                                 
abs   ffffffff  ___c_args__                                                
abs   ffffffff  ___pinit__                                                 
abs   ffffffff  binit                                                      
abs   ffffffff  pinit                                                      

[987 symbols]
