/**
 * @file MotorControlTest.h
 * @brief Header file for motor control interface tests
 * @note This file provides test functions for the motor control abstraction layer
 */

#ifndef MOTOR_CONTROL_TEST_H
#define MOTOR_CONTROL_TEST_H

/* Test result structure */
typedef struct {
    unsigned int testsPassed;
    unsigned int testsFailed;
    unsigned int totalTests;
} TestResults;

/* Test function prototypes */
void MotorControlTest_Initialization(void);
void MotorControlTest_ModeSwitching(void);
void MotorControlTest_RunFunction(void);
void MotorControlTest_MotorRunIntegration(void);
void MotorControlTest_RunAllTests(void);
void MotorControlTest_SimpleRunner(void);

/* Test result functions */
TestResults MotorControlTest_GetResults(void);

#endif /* MOTOR_CONTROL_TEST_H */
